#!/usr/bin/env python3
"""
Component Finder GUI - Interactive Interface

Features:
1. Enter manufacturer name - auto-find website or ask for manual input
2. Enter part number and search
3. Interactive comments and results area
4. Work together to learn new manufacturer patterns
5. Organized file storage (datasheets and 3D models in separate folders)

Usage:
    python component_finder_gui.py
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog, font
import threading
import requests
from bs4 import BeautifulSoup
# Excel support - will be imported when needed
# import pandas as pd
# import openpyxl

# Enhanced web scraping support - will be imported when needed
# from selenium import webdriver
# from selenium.webdriver.common.by import By
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriver.support import expected_conditions as EC
# from selenium.webdriver.chrome.options import Options
import re
import os
import json
import time
import csv
from pathlib import Path
from urllib.parse import urljoin, urlparse, quote, parse_qs
from datetime import datetime
import webbrowser
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComponentFinderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Component Finder - Interactive Learning System")
        self.root.geometry("1400x900")  # Made even wider for better display
        
        # Create directories
        self.datasheet_dir = Path("datasheets")
        self.model_3d_dir = Path("3d")
        self.datasheet_dir.mkdir(exist_ok=True)
        self.model_3d_dir.mkdir(exist_ok=True)
        
        # Initialize session and knowledge base
        self.session = self._create_session()
        self.knowledge_base_file = Path("manufacturer_knowledge.json")
        self.knowledge_base = self._load_knowledge_base()

        # Initialize CSV files
        self.csv_file = Path("actual-web-site-xref.csv")
        self.manufacturer_websites = self._load_manufacturer_csv()

        # Initialize CSV file for tracking found files
        self.found_files_csv = Path("found-files-log.csv")
        self._initialize_found_files_csv()

        self.setup_gui()

        # Add WURTH patterns after GUI is ready
        self._add_wurth_patterns()
        
        # Add initial message
        self.add_comment("🚀 Component Finder GUI Started")
        self.add_comment(f"📁 Working directory: {os.getcwd()}")
        self.add_comment("📁 Created directories: datasheets/ and 3d/")
        self.add_comment("📚 Loaded knowledge base")
        self.add_comment(f"📋 Loaded {len(self.manufacturer_websites)} manufacturer websites from CSV")

        # Initialize matrix search variables
        self.excel_df = None
        self.excel_file_path = None
        self.matrix_search_enabled = False

        # Initialize download pattern learning system
        self.download_patterns = self.load_download_patterns()
        self.learning_mode = True

        # Initialize help system with external text files
        self.help_dir = Path("help_files")
        self.help_dir.mkdir(exist_ok=True)
        self.create_help_files()

        self.add_comment("Ready to search for components!")
        self.add_comment("💡 Use '📊 Load Excel File' for matrix-based search!")
        self.add_comment(f"📚 Help files available in: {self.help_dir}")
    
    def _create_session(self):
        """Create a requests session with comprehensive headers to avoid rate limiting"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'Referer': 'https://www.google.com/'
        })
        return session
    
    def _load_knowledge_base(self):
        if self.knowledge_base_file.exists():
            try:
                with open(self.knowledge_base_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading knowledge base: {e}")
        return {}
    
    def _save_knowledge_base(self):
        try:
            with open(self.knowledge_base_file, 'w') as f:
                json.dump(self.knowledge_base, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving knowledge base: {e}")

    def create_help_files(self):
        """Create external help text files that can be edited by user"""
        help_files = {
            "quick_start.txt": self.get_quick_start_help(),
            "learning_system.txt": self.get_learning_system_help(),
            "excel_setup.txt": self.get_excel_setup_help(),
            "troubleshooting.txt": self.get_troubleshooting_help(),
            "about.txt": self.get_about_help()
        }

        for filename, content in help_files.items():
            filepath = self.help_dir / filename

            # Only create if file doesn't exist (preserve user edits)
            if not filepath.exists():
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(content)
                    self.add_comment(f"📄 Created help file: {filename}")
                except Exception as e:
                    self.add_comment(f"❌ Error creating {filename}: {str(e)[:30]}")

    def load_help_file(self, filename):
        """Load help content from external text file"""
        filepath = self.help_dir / filename

        try:
            if filepath.exists():
                with open(filepath, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return f"Help file not found: {filename}\nPlease check the help_files directory."
        except Exception as e:
            return f"Error loading help file {filename}: {str(e)}"

    def get_quick_start_help(self):
        """Generate default Quick Start help content"""
        return """🚀 COMPONENT FINDER - QUICK START GUIDE

═══════════════════════════════════════════════════════════════

🎯 MAIN PURPOSE:
   Automatically find and download datasheets and 3D STEP files
   for electronic components from multiple sources.

🔍 SINGLE PART SEARCH:
1. Enter Manufacturer and Part Number
2. Click "🔍 Search Component"
3. System searches Digi-Key, Mouser, and manufacturer websites

📊 EXCEL BATCH SEARCH:
1. Click "📊 Load Excel File"
2. Select your Excel file with components
3. Click "🔍 Excel Part File Search"
4. System processes all parts automatically

🎓 LEARNING SYSTEM:
   When files can't be found automatically, the system will:
   • Open the webpage in your browser
   • Ask you to find the download link manually
   • Learn the pattern for future automatic downloads
   • Save patterns in download_patterns.json

📁 FILE ORGANIZATION:
   • Datasheets: datasheets/ folder
   • STEP files: 3d/ folder
   • Results logged in: found-files-log.csv

💡 TIPS:
   • Use exact manufacturer names (e.g., "Diodes Incorporated" not "Diodes")
   • Check spelling of part numbers carefully
   • The system gets smarter with each search
   • Click "📚 Show Knowledge" to see learned manufacturers

═══════════════════════════════════════════════════════════════

🆘 NEED HELP?
   • Click other help tabs for detailed information
   • Try the example: Diodes Incorporated + APX803L20-30SA-7
   • Check the troubleshooting tab if something doesn't work

═══════════════════════════════════════════════════════════════"""

    def get_learning_system_help(self):
        """Generate default Learning System help content"""
        return """🎓 INTERACTIVE LEARNING SYSTEM - DETAILED GUIDE

═══════════════════════════════════════════════════════════════

🧠 HOW THE LEARNING SYSTEM WORKS:

The system learns from YOU when automatic downloads fail:

1. 🔍 SYSTEM SEARCHES automatically first
2. ❌ IF DOWNLOAD FAILS but part is found on website
3. 🎓 LEARNING MODE ACTIVATES
4. 🌐 WEBPAGE OPENS in your browser
5. 👤 YOU FIND the download link manually
6. 📚 SYSTEM LEARNS the pattern
7. 🚀 FUTURE SEARCHES use learned patterns automatically

═══════════════════════════════════════════════════════════════

🎯 WHEN LEARNING MODE ACTIVATES:

📄 DATASHEETS:
   • Searches Digi-Key, Mouser first
   • If not found → Learning mode on Digi-Key/Mouser/Google

🔧 STEP FILES:
   • WURTH: Searches we-online.com → Learning mode if needed
   • UltraLibrarian: Searches ultralibrarian.com → Learning mode
   • SnapMagic/SnapEDA: Searches both sites → Learning mode
   • SamacSys: Searches componentsearchengine.com → Learning mode

═══════════════════════════════════════════════════════════════

📋 LEARNING DIALOG FLOW:

STEP 1 - Learning Activation:
┌─────────────────────────────────────────────────────────────┐
│ 🎓 LEARNING MODE ACTIVATED                                  │
│                                                             │
│ Part found: C1005X5R1E105K050BC                            │
│ Website: UltraLibrarian                                     │
│                                                             │
│ The webpage is now open in your browser.                   │
│ Please look for the STEP file download link.               │
│                                                             │
│ Did you find a STEP file download?                         │
│                                                             │
│ [YES] [NO] [CANCEL]                                         │
└─────────────────────────────────────────────────────────────┘

STEP 2 - If YES, Pattern Capture:
┌─────────────────────────────────────────────────────────────┐
│ 📚 PATTERN LEARNING                                         │
│                                                             │
│ Please copy the download URL from your browser:            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ https://www.ultralibrarian.com/download/123456.step    │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [SAVE PATTERN] [CANCEL]                                     │
└─────────────────────────────────────────────────────────────┘

═══════════════════════════════════════════════════════════════

💾 LEARNED PATTERNS STORAGE:

📄 download_patterns.json:
   • Stores successful download URLs and methods
   • Organized by website and part type
   • Persistent between sessions
   • Success tracking (patterns that work get priority)
   • Manual patterns can be edited in JSON file

🎯 TESTING:
   • Click "🎓 Test Learning" to see learning dialogs
   • No real searches performed - just shows the interface

═══════════════════════════════════════════════════════════════

🔧 ADVANCED LEARNING FEATURES:

📚 Knowledge Base (manufacturer_knowledge.json):
   • Saves successful search URLs for each manufacturer
   • Learns datasheet and STEP file patterns
   • Tracks success rates and last update times
   • Automatically reused for future searches

📋 Website Cross-Reference (actual-web-site-xref.csv):
   • Maps manufacturer names to their websites
   • Built automatically from successful searches
   • Can be manually edited to add known websites
   • Prevents repeated website discovery

═══════════════════════════════════════════════════════════════"""

    def get_excel_setup_help(self):
        """Generate default Excel Setup help content"""
        return """📊 EXCEL INTEGRATION - SETUP & USAGE GUIDE

═══════════════════════════════════════════════════════════════

📋 EXCEL FILE REQUIREMENTS:

REQUIRED COLUMNS:
   • Column F: Manufacturer Name
   • Column G: Part Number

OPTIONAL COLUMNS (will be updated by system):
   • Column H: Datasheet Path (updated when found)
   • Column J: 3D Model Source (updated when found)
   • Column K: 3D Model Filename (updated when found)

═══════════════════════════════════════════════════════════════

🔧 SETUP STEPS:

1. PREPARE YOUR EXCEL FILE:
   ✅ Put manufacturer names in Column F
   ✅ Put part numbers in Column G
   ✅ Save as .xlsx format (Excel 2007+)
   ✅ Close the file before loading into the program

2. LOAD THE FILE:
   ✅ Click "📊 Load Excel File"
   ✅ Select your Excel file
   ✅ System will show how many parts were found

3. START BATCH SEARCH:
   ✅ Click "🔍 Excel Part File Search"
   ✅ System processes each part automatically
   ✅ Watch progress in the comments area

═══════════════════════════════════════════════════════════════

📈 WHAT HAPPENS DURING BATCH SEARCH:

FOR MISSING STEP FILES (Column K = "nan" or blank):
   ✅ Searches manufacturer websites
   ✅ Searches alternative sources (UltraLibrarian, SnapMagic, etc.)
   ✅ Updates Column K with filename
   ✅ Updates Column J with manufacturer + source

FOR MISSING DATASHEETS (Column H = "nan" or blank):
   ✅ Searches Digi-Key, Mouser
   ✅ If not found, enters learning mode
   ✅ Updates Column H with "datasheets\\filename.pdf"

═══════════════════════════════════════════════════════════════

💾 FILE FORMATS SUPPORTED:

✅ .xlsx (Excel 2007+) - RECOMMENDED
✅ .xls (Excel 97-2003)
❌ .csv files (use Excel to convert)

═══════════════════════════════════════════════════════════════

📊 EXAMPLE EXCEL STRUCTURE:

| A | B | C | D | E | F                    | G                  | H            | I | J              | K                |
|---|---|---|---|---|----------------------|--------------------|--------------|---|----------------|------------------|
| 1 | 2 | 3 | 4 | 5 | Diodes Incorporated  | APX803L20-30SA-7   | datasheets\\...| 9 | Diodes Inc     | APX803L.step     |
| 1 | 2 | 3 | 4 | 5 | TDK                  | C1005X5R1E105K050BC| datasheets\\...| 9 | UltraLibrarian | C1005X5R.step    |

═══════════════════════════════════════════════════════════════

🔍 TROUBLESHOOTING EXCEL ISSUES:

❌ "No Excel file loaded":
   Solution: Click "📊 Load Excel File" first

❌ "No parts found in Excel":
   Solution: Check Columns F and G have data, ensure no extra spaces

❌ "Excel file is locked":
   Solution: Close Excel completely before loading file

❌ "Permission denied":
   Solution: Make sure Excel file is not read-only

═══════════════════════════════════════════════════════════════"""

    def get_troubleshooting_help(self):
        """Generate default Troubleshooting help content"""
        return """🔧 TROUBLESHOOTING GUIDE

═══════════════════════════════════════════════════════════════

🚨 COMMON ISSUES & SOLUTIONS:

🔍 SEARCH ISSUES:
   Problem: No datasheets found
   Solution: Check manufacturer name spelling, try learning mode

   Problem: No STEP files found
   Solution: Enable alternative sources search, use learning mode

   Problem: Downloads fail
   Solution: Check internet connection, try manual learning

🎓 LEARNING SYSTEM ISSUES:
   Problem: Learning dialogs don't appear
   Solution: Ensure learning_mode = True, check if part actually found on website

   Problem: Learned patterns don't work
   Solution: Check download_patterns.json file, patterns may need updating

   Problem: Browser doesn't open
   Solution: Check default browser settings, try different browser

📊 EXCEL ISSUES:
   Problem: Excel file won't load
   Solution: Close Excel completely, check file format (.xlsx recommended)

   Problem: Parts not found in Excel
   Solution: Check Column G has part numbers, ensure no extra spaces

   Problem: Updates not saving to Excel
   Solution: Make sure Excel file is not open in Excel while program runs

🌐 NETWORK ISSUES:
   Problem: Timeouts or connection errors
   Solution: Check internet connection, try again later

   Problem: Websites block requests
   Solution: Wait a few minutes between searches, use learning mode

═══════════════════════════════════════════════════════════════

📁 FILE LOCATIONS:
   • Datasheets: datasheets\\ folder
   • STEP files: 3d\\ folder
   • Patterns: download_patterns.json
   • Websites: actual-web-site-xref.csv
   • Knowledge: manufacturer_knowledge.json
   • Log: found-files-log.csv
   • Help: help_files\\ folder

═══════════════════════════════════════════════════════════════

🔄 RESET OPTIONS:
   • Delete download_patterns.json to reset learned patterns
   • Delete manufacturer_knowledge.json to reset website knowledge
   • Click "🧹 Cleanup Files" to organize downloaded files

═══════════════════════════════════════════════════════════════

📞 GETTING HELP:
   • Click "🎓 Test Learning" to test learning system
   • Check console output for detailed error messages
   • Learning mode shows step-by-step what's happening
   • Patterns are saved in human-readable JSON format
   • Edit help files in help_files\\ folder to customize this help

═══════════════════════════════════════════════════════════════"""

    def get_about_help(self):
        """Generate default About help content"""
        return """ℹ️ ABOUT COMPONENT FINDER

═══════════════════════════════════════════════════════════════

🎯 COMPONENT FINDER v3.2
   Automatically find and download datasheets and 3D STEP files
   for electronic components from multiple sources.

🚀 KEY FEATURES:
   ✅ Excel integration for batch processing
   ✅ Interactive learning system
   ✅ Multiple source searching (Digi-Key, Mouser, manufacturer sites)
   ✅ Alternative 3D model sources (UltraLibrarian, SnapMagic, SamacSys)
   ✅ Automatic file organization
   ✅ Pattern learning and reuse
   ✅ Progress tracking and logging
   ✅ Customizable help system

═══════════════════════════════════════════════════════════════

🔍 SEARCH SOURCES:

DATASHEETS:
   • Digi-Key (primary)
   • Mouser (primary)
   • Manufacturer websites
   • Learning mode for custom sources

STEP FILES:
   • WURTH: we-online.com
   • UltraLibrarian: ultralibrarian.com
   • SnapMagic/SnapEDA: snapmagic.com, snapeda.com
   • SamacSys: componentsearchengine.com
   • Manufacturer websites
   • Learning mode for custom sources

═══════════════════════════════════════════════════════════════

🧠 LEARNING SYSTEM:
   The system learns from your manual actions:
   • Saves download patterns to download_patterns.json
   • Reuses patterns for similar parts
   • Tracks success rates
   • Continuously improves accuracy

📁 FILE ORGANIZATION:
   • Automatic folder creation
   • Consistent naming conventions
   • Duplicate handling
   • Progress logging

📊 EXCEL INTEGRATION:
   • Batch processing of component lists
   • Automatic spreadsheet updates
   • Progress tracking
   • Resume capability

═══════════════════════════════════════════════════════════════

📊 VERSION HISTORY:
   • v1.0: Basic component search
   • v2.0: Excel integration
   • v3.0: Interactive learning system
   • v3.1: Enhanced alternative sources
   • v3.2: Comprehensive help system + External help files

═══════════════════════════════════════════════════════════════

🎓 LEARNING PHILOSOPHY:
   "The system gets smarter with every manual intervention"

   Instead of failing silently, the system asks for help,
   learns from your actions, and becomes more capable over time.

═══════════════════════════════════════════════════════════════

📝 CUSTOMIZING HELP:
   You can edit the help content by modifying files in the help_files\\ folder:
   • quick_start.txt - Quick start guide
   • learning_system.txt - Learning system details
   • excel_setup.txt - Excel integration guide
   • troubleshooting.txt - Common issues and solutions
   • about.txt - This about page

   Changes will appear immediately when you restart the program.

═══════════════════════════════════════════════════════════════"""

    def _add_wurth_patterns(self):
        """Add WURTH/Würth Elektronik patterns to knowledge base"""
        wurth_key = "wurth"

        # Always update with the correct search URL we just discovered
        self.knowledge_base[wurth_key] = {
            "name": "Würth Elektronik",
            "base_url": "https://www.we-online.com",
            "search_url_format": "https://www.we-online.com/en/components/products?sq={part_number}",
            "search_patterns": {
                "successful_urls": [
                    "/en/components/products?sq={part_number}",
                    "/catalog/en/products/{part_number}",
                    "/catalog/products/{part_number}",
                    "/en/products/{part_number}",
                    "/products/{part_number}"
                ],
                "search_methods": ["simple_search", "direct_url", "catalog_search"],
                "datasheet_patterns": [
                    r'href="([^"]*\.pdf[^"]*)"[^>]*>.*?datasheet',
                    r'href="([^"]*datasheet[^"]*\.pdf)"',
                    r'href="([^"]*catalog[^"]*\.pdf)"',
                    r'href="([^"]*media[^"]*\.pdf)"'
                ],
                "model_3d_patterns": [
                    r'href="([^"]*\.step)"',
                    r'href="([^"]*\.stp)"',
                    r'href="([^"]*3d[^"]*\.step)"',
                    r'href="([^"]*3d[^"]*\.stp)"'
                ]
            },
            "package_detection": {
                "patterns": [
                    r'package[:\s]*([a-z0-9\-]+)',
                    r'footprint[:\s]*([a-z0-9\-]+)',
                    r'housing[:\s]*([a-z0-9\-]+)'
                ]
            },
            "known_parts": {
                # Will be populated as we find parts
            },
            "last_successful_search": "https://www.we-online.com/en/components/products?sq=************",
            "last_updated": "2025-01-23"
        }
        self._save_knowledge_base()
        self.add_comment("📚 Updated WURTH search patterns in knowledge base")

    def _load_manufacturer_csv(self):
        """Load manufacturer website mappings from CSV file"""
        manufacturer_websites = {}

        if self.csv_file.exists():
            try:
                with open(self.csv_file, 'r', newline='', encoding='utf-8') as f:
                    reader = csv.reader(f)

                    # Skip header if it exists
                    first_row = next(reader, None)
                    if first_row and first_row[0].lower() in ['manufacturer', 'manufacturer name']:
                        pass  # Skip header row
                    else:
                        # First row is data, process it
                        if first_row and len(first_row) >= 2:
                            manufacturer_websites[first_row[0].lower().strip()] = first_row[1].strip()

                    # Process remaining rows
                    for row in reader:
                        if len(row) >= 2 and row[0].strip() and row[1].strip():
                            manufacturer_name = row[0].strip()
                            website = row[1].strip()
                            manufacturer_websites[manufacturer_name.lower()] = website

                logger.info(f"Loaded {len(manufacturer_websites)} manufacturer websites from CSV")

            except Exception as e:
                logger.error(f"Error loading manufacturer CSV: {e}")
                self._create_default_csv()
        else:
            # Create default CSV file with some common manufacturers
            self._create_default_csv()

        return manufacturer_websites

    def _create_default_csv(self):
        """Create default CSV file with common manufacturers"""
        default_manufacturers = [
            ["Manufacturer Name", "Website"],
            ["Diodes Inc", "https://www.diodes.com"],
            ["Texas Instruments", "https://www.ti.com"],
            ["Analog Devices", "https://www.analog.com"],
            ["Microchip Technology", "https://www.microchip.com"],
            ["STMicroelectronics", "https://www.st.com"],
            ["Infineon", "https://www.infineon.com"],
            ["NXP", "https://www.nxp.com"],
            ["ON Semiconductor", "https://www.onsemi.com"],
            ["Maxim Integrated", "https://www.maximintegrated.com"],
            ["Linear Technology", "https://www.linear.com"]
        ]

        try:
            with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerows(default_manufacturers)

            logger.info(f"Created default manufacturer CSV file: {self.csv_file}")
            self.add_comment(f"📋 Created default manufacturer CSV with {len(default_manufacturers)-1} entries")

            # Reload the CSV
            self.manufacturer_websites = self._load_manufacturer_csv()

        except Exception as e:
            logger.error(f"Error creating default CSV: {e}")

    def _save_manufacturer_to_csv(self, manufacturer_name, website):
        """Save a new manufacturer website mapping to CSV"""
        try:
            # Check if manufacturer already exists
            manufacturer_key = manufacturer_name.lower().strip()

            if manufacturer_key in self.manufacturer_websites:
                # Update existing entry
                self.manufacturer_websites[manufacturer_key] = website
                self.add_comment(f"📋 Updated {manufacturer_name} website in CSV")
            else:
                # Add new entry
                self.manufacturer_websites[manufacturer_key] = website
                self.add_comment(f"📋 Added {manufacturer_name} to CSV")

            # Rewrite the entire CSV file
            rows = [["Manufacturer Name", "Website"]]

            # Sort manufacturers alphabetically for better organization
            sorted_manufacturers = sorted(self.manufacturer_websites.items())

            for manufacturer_key, website in sorted_manufacturers:
                # Convert key back to proper case (try to preserve original case)
                display_name = manufacturer_name if manufacturer_key == manufacturer_name.lower() else manufacturer_key.title()
                rows.append([display_name, website])

            with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerows(rows)

            logger.info(f"Saved manufacturer {manufacturer_name} to CSV")

        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            self.add_comment(f"❌ Failed to save {manufacturer_name} to CSV: {e}")

    def _initialize_found_files_csv(self):
        """Initialize the found files CSV with headers if it doesn't exist"""
        if not self.found_files_csv.exists():
            try:
                headers = [
                    "Manufacturer Name",
                    "Part Number",
                    "Datasheet URL",
                    "Datasheet Filename",
                    "3D Model URL",
                    "3D Model Filename",
                    "Package Type",
                    "Date Found",
                    "Search Success"
                ]

                with open(self.found_files_csv, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(headers)

                logger.info(f"Created found files CSV: {self.found_files_csv}")
                self.add_comment(f"📋 Created found files log: {self.found_files_csv}")

            except Exception as e:
                logger.error(f"Error creating found files CSV: {e}")

    def _log_found_files(self, manufacturer, part_number, datasheet_url, datasheet_filename,
                        model_url, model_filename, package_type, success):
        """Log found files to the CSV"""
        try:
            import datetime

            # Prepare the row data
            row_data = [
                manufacturer,
                part_number,
                datasheet_url or "",
                datasheet_filename or "",
                model_url or "",
                model_filename or "",
                package_type or "",
                datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Success" if success else "Partial/Failed"
            ]

            # Append to CSV file
            with open(self.found_files_csv, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(row_data)

            self.add_comment(f"📋 Logged search results to found-files-log.csv")
            logger.info(f"Logged found files for {manufacturer} {part_number}")

        except Exception as e:
            logger.error(f"Error logging to found files CSV: {e}")
            self.add_comment(f"⚠️ Could not log to found files CSV: {e}")
    
    def setup_gui(self):
        # Configure button styles with bigger icons, black text, white background
        style = ttk.Style()

        # Set theme to ensure compatibility
        try:
            style.theme_use('clam')  # Use clam theme for better color support
        except:
            pass  # Fall back to default theme

        # Create button styles with black text on white background and bigger icons
        style.configure('Search.TButton',
                       foreground='black',
                       background='white',
                       focuscolor='none',
                       borderwidth=2,
                       relief='raised',
                       font=('Arial', 10, 'bold'))

        style.configure('Clear.TButton',
                       foreground='black',
                       background='white',
                       focuscolor='none',
                       borderwidth=2,
                       relief='raised',
                       font=('Arial', 10, 'bold'))

        style.configure('Help.TButton',
                       foreground='black',
                       background='white',
                       focuscolor='none',
                       borderwidth=2,
                       relief='raised',
                       font=('Arial', 10, 'bold'))

        style.configure('Knowledge.TButton',
                       foreground='black',
                       background='white',
                       focuscolor='none',
                       borderwidth=2,
                       relief='raised',
                       font=('Arial', 10, 'bold'))

        style.configure('CSV.TButton',
                       foreground='black',
                       background='white',
                       focuscolor='none',
                       borderwidth=2,
                       relief='raised',
                       font=('Arial', 10, 'bold'))

        style.configure('Excel.TButton',
                       foreground='black',
                       background='white',
                       focuscolor='none',
                       borderwidth=2,
                       relief='raised',
                       font=('Arial', 10, 'bold'))

        style.configure('Cleanup.TButton',
                       foreground='black',
                       background='white',
                       focuscolor='none',
                       borderwidth=2,
                       relief='raised',
                       font=('Arial', 10, 'bold'))

        style.configure('Learning.TButton',
                       foreground='black',
                       background='white',
                       focuscolor='none',
                       borderwidth=2,
                       relief='raised',
                       font=('Arial', 10, 'bold'))

        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)  # Row 6 is now the large text area
        
        # Manufacturer input (closer to text with reduced padding)
        ttk.Label(main_frame, text="Manufacturer:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.manufacturer_var = tk.StringVar(value="Texas Instruments")
        self.manufacturer_entry = ttk.Entry(main_frame, textvariable=self.manufacturer_var, width=25)
        self.manufacturer_entry.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(2, 0))

        # Website input (initially hidden)
        self.website_label = ttk.Label(main_frame, text="Website URL:")
        self.website_var = tk.StringVar()
        self.website_entry = ttk.Entry(main_frame, textvariable=self.website_var, width=25)

        # Part number input (closer to text with reduced padding)
        ttk.Label(main_frame, text="Part Number:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.part_number_var = tk.StringVar(value="LM358N")
        self.part_number_entry = ttk.Entry(main_frame, textvariable=self.part_number_var, width=25)
        self.part_number_entry.grid(row=1, column=1, sticky=tk.W, pady=2, padx=(2, 0))
        
        # Buttons frame - 2 rows, left justified
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10, sticky='w')

        # Row 1 - Main action buttons (bigger emojis, black text, white background)
        self.search_button = ttk.Button(button_frame, text="🔍  Search Component", command=self.start_search, style='Search.TButton')
        self.search_button.grid(row=0, column=0, padx=5, pady=2, sticky='w')

        self.clear_button = ttk.Button(button_frame, text="🗑️  Clear Results", command=self.clear_results, style='Clear.TButton')
        self.clear_button.grid(row=0, column=1, padx=5, pady=2, sticky='w')

        self.help_button = ttk.Button(button_frame, text="❓  Help", command=self.show_help, style='Help.TButton')
        self.help_button.grid(row=0, column=2, padx=5, pady=2, sticky='w')

        self.knowledge_button = ttk.Button(button_frame, text="🧠  Show Knowledge", command=self.show_knowledge, style='Knowledge.TButton')
        self.knowledge_button.grid(row=0, column=3, padx=5, pady=2, sticky='w')

        # Row 2 - Data management buttons (bigger emojis, black text, white background)
        self.csv_button = ttk.Button(button_frame, text="📋  Edit Websites", command=self.open_csv_file, style='CSV.TButton')
        self.csv_button.grid(row=1, column=0, padx=5, pady=2, sticky='w')

        self.excel_button = ttk.Button(button_frame, text="📊  Load Excel File", command=self.load_excel_file, style='Excel.TButton')
        self.excel_button.grid(row=1, column=1, padx=5, pady=2, sticky='w')

        self.cleanup_button = ttk.Button(button_frame, text="🧹  Cleanup Files", command=self.manual_cleanup, style='Cleanup.TButton')
        self.cleanup_button.grid(row=1, column=2, padx=5, pady=2, sticky='w')

        self.test_learning_button = ttk.Button(button_frame, text="🎓  Test Learning", command=self.test_learning_system, style='Learning.TButton')
        self.test_learning_button.grid(row=1, column=3, padx=5, pady=2, sticky='w')
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # Status and results fields
        status_frame = ttk.LabelFrame(main_frame, text="Search Status", padding="5")
        status_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        status_frame.columnconfigure(1, weight=1)

        # Working status field (good width for file paths)
        ttk.Label(status_frame, text="Status:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.status_text = tk.StringVar(value="Ready")
        status_entry = ttk.Entry(status_frame, textvariable=self.status_text, state='readonly', width=40)
        status_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))

        # Found PDF file field (good width for file paths)
        ttk.Label(status_frame, text="Datasheet:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.pdf_file_text = tk.StringVar(value="")
        pdf_entry = ttk.Entry(status_frame, textvariable=self.pdf_file_text, state='readonly', width=40)
        pdf_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))

        # Found STEP file field (good width for file paths)
        ttk.Label(status_frame, text="3D Model:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.step_file_text = tk.StringVar(value="")
        step_entry = ttk.Entry(status_frame, textvariable=self.step_file_text, state='readonly', width=40)
        step_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        
        # Results and comments area - LARGE TEXT AREA WITH COLOR
        results_label = ttk.Label(main_frame, text="📝 Comments and Results:", foreground='#2196F3', font=('Arial', 10, 'bold'))
        results_label.grid(row=5, column=0, sticky=(tk.W, tk.N), pady=(10, 5))

        # Large scrollable text area for results and comments with colored background and emoji support
        # Try different fonts that support colored emojis on Windows
        emoji_fonts = [
            ('Segoe UI Emoji', 10),
            ('Apple Color Emoji', 10),
            ('Noto Color Emoji', 10),
            ('Segoe UI', 10),
            ('Arial', 10)
        ]

        emoji_font = emoji_fonts[0]  # Default to first font
        for font_option in emoji_fonts:
            try:
                # Test if font is available
                test_font = tk.font.Font(family=font_option[0], size=font_option[1])
                emoji_font = font_option
                break
            except:
                continue

        self.results_text = scrolledtext.ScrolledText(main_frame, height=20, width=80, wrap=tk.WORD,
                                                     bg='#f8f9fa', fg='#212529',
                                                     font=emoji_font)
        self.results_text.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))

        # Add initial welcome message with colors
        self.results_text.insert(tk.END, "🚀 Component Finder Ready!\n", 'header')
        self.results_text.insert(tk.END, "Enter manufacturer and part number, then click 'Search Component'\n")
        self.results_text.insert(tk.END, "=" * 60 + "\n")

        # Configure text tags for colored output with colored backgrounds for emojis
        self.results_text.tag_configure('header', foreground='#4CAF50', font=emoji_font, background='#E8F5E8')
        self.results_text.tag_configure('success', foreground='#4CAF50', font=emoji_font, background='#E8F5E8')
        self.results_text.tag_configure('error', foreground='#f44336', font=emoji_font, background='#FFEBEE')
        self.results_text.tag_configure('warning', foreground='#FF9800', font=emoji_font, background='#FFF3E0')
        self.results_text.tag_configure('info', foreground='#2196F3', font=emoji_font, background='#E3F2FD')
        self.results_text.tag_configure('search', foreground='#9C27B0', font=emoji_font, background='#F3E5F5')
        self.results_text.tag_configure('download', foreground='#607D8B', font=emoji_font, background='#ECEFF1')

        # Special tags for emoji-only text with colored backgrounds
        self.results_text.tag_configure('emoji_success', foreground='white', background='#4CAF50', font=emoji_font)
        self.results_text.tag_configure('emoji_error', foreground='white', background='#f44336', font=emoji_font)
        self.results_text.tag_configure('emoji_warning', foreground='black', background='#FF9800', font=emoji_font)
        self.results_text.tag_configure('emoji_search', foreground='white', background='#9C27B0', font=emoji_font)
        self.results_text.tag_configure('emoji_download', foreground='white', background='#607D8B', font=emoji_font)
        self.results_text.tag_configure('emoji_info', foreground='white', background='#2196F3', font=emoji_font)

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
    
    def add_comment(self, message):
        """Add a comment to the results area with colored emoji backgrounds"""
        timestamp = time.strftime("%H:%M:%S")

        # Insert timestamp
        self.results_text.insert(tk.END, f"[{timestamp}] ")

        # Process message to add colored backgrounds to emojis
        if "✅" in message or "SUCCESS" in message.upper() or "Found" in message:
            self._insert_colored_message(message, 'success', 'emoji_success')
        elif "❌" in message or "ERROR" in message.upper() or "Failed" in message:
            self._insert_colored_message(message, 'error', 'emoji_error')
        elif "⚠️" in message or "WARNING" in message.upper():
            self._insert_colored_message(message, 'warning', 'emoji_warning')
        elif "🔍" in message or "Searching" in message or "Trying" in message:
            self._insert_colored_message(message, 'search', 'emoji_search')
        elif "📥" in message or "Download" in message or "Progress" in message:
            self._insert_colored_message(message, 'download', 'emoji_download')
        elif "ℹ️" in message or "INFO" in message.upper():
            self._insert_colored_message(message, 'info', 'emoji_info')
        else:
            self.results_text.insert(tk.END, message)

        self.results_text.insert(tk.END, "\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def _insert_colored_message(self, message, text_tag, emoji_tag):
        """Insert message with colored emoji backgrounds"""
        # List of emojis to colorize
        emojis = ['✅', '❌', '⚠️', '🔍', '📥', 'ℹ️', '🚀', '📄', '📦', '🧠', '📋', '📊', '🗑️', '❓', '🎓', '🔧', '💾', '📝']

        i = 0
        while i < len(message):
            char = message[i]
            if char in emojis:
                # Insert emoji with colored background
                self.results_text.insert(tk.END, f" {char} ", emoji_tag)
            else:
                # Check for multi-character emojis
                found_emoji = False
                for emoji in emojis:
                    if message[i:].startswith(emoji):
                        self.results_text.insert(tk.END, f" {emoji} ", emoji_tag)
                        i += len(emoji) - 1
                        found_emoji = True
                        break

                if not found_emoji:
                    # Insert regular text with text color
                    self.results_text.insert(tk.END, char, text_tag)
            i += 1
    
    def clear_results(self):
        """Clear the results area"""
        self.results_text.delete(1.0, tk.END)
        self.add_comment("🗑️ Results cleared")
    
    def show_knowledge(self):
        """Show the current knowledge base"""
        self.add_comment("📚 Current Knowledge Base:")
        self.add_comment("=" * 50)
        
        if not self.knowledge_base:
            self.add_comment("No manufacturers learned yet")
        else:
            for manufacturer, data in self.knowledge_base.items():
                self.add_comment(f"🏭 {data['name']}:")
                self.add_comment(f"   Base URL: {data['base_url']}")
                self.add_comment(f"   Success Count: {data['success_count']}")
                self.add_comment(f"   Search URLs: {len(data['search_patterns']['successful_urls'])}")
                self.add_comment(f"   Datasheet Patterns: {len(data['datasheet_patterns']['url_patterns'])}")
                self.add_comment(f"   3D Model Patterns: {len(data['3d_model_patterns']['url_patterns'])}")
                self.add_comment("")

    def open_csv_file(self):
        """Open the CSV file for editing"""
        try:
            import subprocess
            import platform

            csv_path = str(self.csv_file.absolute())

            # Show current CSV contents first
            self.add_comment("📋 Current manufacturer websites:")
            self.add_comment("=" * 40)

            if self.csv_file.exists():
                with open(self.csv_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for i, line in enumerate(lines[:10]):  # Show first 10 lines
                        self.add_comment(f"   {line.strip()}")
                    if len(lines) > 10:
                        self.add_comment(f"   ... and {len(lines)-10} more entries")

            self.add_comment("=" * 40)
            self.add_comment(f"📂 Opening CSV file: {csv_path}")

            # Open file with default application
            if platform.system() == 'Windows':
                os.startfile(csv_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', csv_path])
            else:  # Linux
                subprocess.run(['xdg-open', csv_path])

            self.add_comment("✅ CSV file opened in default application")
            self.add_comment("💡 Edit the file and save it. Changes will be loaded on next search.")

            # Add reload button functionality
            result = messagebox.askyesno(
                "CSV File Opened",
                f"The CSV file has been opened in your default application.\n\n"
                f"File: {csv_path}\n\n"
                f"After editing and saving the file, would you like to reload it now?"
            )

            if result:
                self.reload_csv_file()

        except Exception as e:
            self.add_comment(f"❌ Failed to open CSV file: {e}")
            messagebox.showerror("Error", f"Could not open CSV file:\n{e}")

    def reload_csv_file(self):
        """Reload the CSV file"""
        try:
            old_count = len(self.manufacturer_websites)
            self.manufacturer_websites = self._load_manufacturer_csv()
            new_count = len(self.manufacturer_websites)

            self.add_comment(f"🔄 Reloaded CSV file: {old_count} → {new_count} entries")

            if new_count != old_count:
                self.add_comment("✅ CSV file updated successfully!")
            else:
                self.add_comment("📋 No changes detected in CSV file")

        except Exception as e:
            self.add_comment(f"❌ Failed to reload CSV file: {e}")
            messagebox.showerror("Error", f"Could not reload CSV file:\n{e}")

    def manual_cleanup(self):
        """Manual cleanup of files"""
        try:
            # Count existing files
            datasheet_files = list(self.datasheet_dir.glob("*.pdf"))
            model_files = list(self.model_3d_dir.glob("*.step")) + list(self.model_3d_dir.glob("*.stp"))

            total_files = len(datasheet_files) + len(model_files)

            if total_files == 0:
                messagebox.showinfo("Cleanup", "No files found to clean up.")
                self.add_comment("✨ No files found in datasheets/ or 3d/ directories")
                return

            # Ask user what to clean up
            cleanup_options = [
                "Clean up current part only",
                "Clean up all duplicate files",
                "Clean up everything",
                "Cancel"
            ]

            # Show file counts
            file_info = f"Found files:\n• {len(datasheet_files)} datasheets\n• {len(model_files)} 3D models\n\nWhat would you like to clean up?"

            result = messagebox.askyesnocancel(
                "File Cleanup",
                f"{file_info}\n\nClean up files?",
                icon='question'
            )

            if result is True:  # Yes - clean current part
                manufacturer = self.manufacturer_var.get().strip()
                part_number = self.part_number_var.get().strip()

                if manufacturer and part_number:
                    self._cleanup_existing_files(manufacturer, part_number)
                else:
                    messagebox.showwarning("Cleanup", "Please enter manufacturer and part number first.")

            elif result is False:  # No - clean all duplicates
                self._cleanup_duplicate_files()

            # result is None for Cancel - do nothing

        except Exception as e:
            self.add_comment(f"❌ Cleanup failed: {e}")
            messagebox.showerror("Error", f"Cleanup failed:\n{e}")

    def _cleanup_duplicate_files(self):
        """Clean up duplicate files (files with _1, _2, etc. suffixes)"""
        try:
            deleted_count = 0

            # Clean datasheets
            for file_path in self.datasheet_dir.glob("*_[0-9]*.pdf"):
                try:
                    file_path.unlink()
                    deleted_count += 1
                    self.add_comment(f"🗑️ Deleted duplicate: {file_path.name}")
                except Exception as e:
                    self.add_comment(f"⚠️ Could not delete {file_path.name}: {e}")

            # Clean 3D models
            for pattern in ["*_[0-9]*.step", "*_[0-9]*.stp"]:
                for file_path in self.model_3d_dir.glob(pattern):
                    try:
                        file_path.unlink()
                        deleted_count += 1
                        self.add_comment(f"🗑️ Deleted duplicate: {file_path.name}")
                    except Exception as e:
                        self.add_comment(f"⚠️ Could not delete {file_path.name}: {e}")

            if deleted_count > 0:
                self.add_comment(f"🧹 Cleaned up {deleted_count} duplicate file(s)")
                messagebox.showinfo("Cleanup Complete", f"Deleted {deleted_count} duplicate files.")
            else:
                self.add_comment("✨ No duplicate files found")
                messagebox.showinfo("Cleanup Complete", "No duplicate files found.")

        except Exception as e:
            self.add_comment(f"⚠️ Error during duplicate cleanup: {e}")

    def view_found_files_log(self):
        """View the found files log"""
        try:
            if not self.found_files_csv.exists():
                messagebox.showinfo("No Log", "No found files log exists yet. Search for some components first!")
                return

            # Read and display the CSV contents
            self.add_comment("📊 Found Files Log:")
            self.add_comment("=" * 60)

            with open(self.found_files_csv, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)

                # Read header
                header = next(reader, None)
                if header:
                    self.add_comment(f"📋 {' | '.join(header)}")
                    self.add_comment("-" * 60)

                # Read and display rows
                row_count = 0
                for row in reader:
                    if len(row) >= 5:  # Ensure we have enough columns
                        # Format the row for display
                        manufacturer = row[0][:15] + "..." if len(row[0]) > 15 else row[0]
                        part_number = row[1][:15] + "..." if len(row[1]) > 15 else row[1]
                        datasheet = "✅" if row[3] else "❌"
                        model = "✅" if row[5] else "❌"
                        package = row[6][:8] if row[6] else "N/A"
                        date = row[7][:10] if len(row) > 7 else "N/A"
                        success = row[8] if len(row) > 8 else "Unknown"

                        display_row = f"{manufacturer:<15} | {part_number:<15} | {datasheet} PDF | {model} 3D | {package:<8} | {date} | {success}"
                        self.add_comment(display_row)
                        row_count += 1

                        if row_count >= 20:  # Limit display to prevent overwhelming
                            remaining = sum(1 for _ in reader)
                            if remaining > 0:
                                self.add_comment(f"... and {remaining} more entries")
                            break

            self.add_comment("=" * 60)
            self.add_comment(f"📊 Total entries shown: {row_count}")

            # Offer to open the file
            result = messagebox.askyesno(
                "Found Files Log",
                f"Log contains {row_count}+ entries.\n\nWould you like to open the CSV file in your default application?"
            )

            if result:
                self.open_found_files_csv()

        except Exception as e:
            self.add_comment(f"❌ Error viewing log: {e}")
            messagebox.showerror("Error", f"Could not view found files log:\n{e}")

    def open_found_files_csv(self):
        """Open the found files CSV in default application"""
        try:
            import subprocess
            import platform

            csv_path = str(self.found_files_csv.absolute())

            self.add_comment(f"📂 Opening found files log: {csv_path}")

            # Open file with default application
            if platform.system() == 'Windows':
                os.startfile(csv_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', csv_path])
            else:  # Linux
                subprocess.run(['xdg-open', csv_path])

            self.add_comment("✅ Found files log opened in default application")

        except Exception as e:
            self.add_comment(f"❌ Failed to open found files log: {e}")
            messagebox.showerror("Error", f"Could not open found files log:\n{e}")

    def show_help(self):
        """Show comprehensive help dialog"""
        help_window = tk.Toplevel(self.root)
        help_window.title("Component Finder - Help")
        help_window.geometry("700x600")
        help_window.transient(self.root)
        help_window.grab_set()

        # Center the help window
        help_window.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # Create notebook for tabbed help
        notebook = ttk.Notebook(help_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Tab 1: Basic Usage
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text="🚀 Basic Usage")

        basic_text = scrolledtext.ScrolledText(basic_frame, wrap=tk.WORD, width=80, height=25)
        basic_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        basic_help = """🚀 COMPONENT FINDER - STEP-BY-STEP FOR BEGINNERS

📝 YOUR FIRST SEARCH (EASY EXAMPLE):

STEP 1 - ENTER THE INFORMATION:
   • In the "Manufacturer" box, type: WURTH
   • In the "Part Number" box, type: ************
   • Don't worry about capital letters - the program is smart

STEP 2 - START THE SEARCH:
   • Click the "🔍 Search Component" button
   • You'll see a spinning progress bar
   • Watch the big text area at the bottom for updates

STEP 3 - WATCH WHAT HAPPENS:
   • The program will say "Starting search..."
   • Then "📊 Checking distributors first for part ************..." (NEW!)
   • Then "🔍 Trying simple Digi-Key search for ************..." (NEW!)
   • Then "✅ Found part on Digi-Key: Würth Elektronik" (NEW!)
   • Then "📄 Found datasheet link: https://..." (NEW!)
   • Then "📥 Downloading datasheet from: https://..." (NEW!)
   • Then "✅ Downloaded: Wurth_Elektronik ************_datasheet.pdf" (NEW!)
   • Then "🌐 Using verified website: https://www.we-online.com" (NEW!)
   • Finally "3D model saved" (if available on manufacturer site)

STEP 4 - CHECK YOUR RESULTS:
   • Look at the three boxes under "Search Status"
   • Status box should say "✅ Success - Found datasheet and 3D model"
   • Datasheet box shows the PDF filename
   • 3D Model box shows the STEP filename

STEP 5 - FIND YOUR FILES:
   • Look in the "datasheets" folder for the PDF file
   • Look in the "3d" folder for the STEP file
   • Files have the manufacturer name at the beginning
   • All files are in the web-get-files directory

🎯 WHAT YOU GET:

DATASHEET (PDF FILE):
   • Technical specifications for the component
   • Saved as: "Wurth_Elektronik ************_datasheet.pdf"
   • Located in: datasheets/ folder
   • Can be opened with any PDF reader

3D MODEL (STEP FILE):
   • 3D shape of the component for CAD programs
   • Saved as: "Wurth_Elektronik ************_connector.step"
   • Located in: 3d/ folder
   • Can be imported into CAD software like SolidWorks, Fusion 360

📁 WHERE ARE MY FILES?

The program creates two folders:
   📁 datasheets/ - All your PDF datasheets go here
   📁 3d/ - All your 3D models go here

🚨 WHAT IF IT DOESN'T WORK?

IF THE PROGRAM ASKS FOR A WEBSITE:
   • It doesn't know this manufacturer yet
   • Type the company's main website (like www.diodes.com)
   • The program will remember it for next time

IF IT SAYS "NO FILES FOUND":
   • Try a different part number from the same manufacturer
   • Check the spelling of the manufacturer name
   • Some manufacturers don't provide 3D models

IF YOU GET AN ERROR:
   • Read the error message in the comments area
   • Try clicking "🗑️ Clear Results" and try again
   • Make sure you have internet connection

🎓 EASY MANUFACTURERS TO TRY:

These usually work well for beginners:
   • Diodes Inc (try part: APX803L20-30SA-7)
   • Texas Instruments (try part: LM358)
   • Analog Devices (try part: AD8065)

💡 BEGINNER TIPS:

• Start with the examples above before trying your own parts
• Watch the comments area - it tells you exactly what's happening
• Don't worry if the first search is slow - it gets faster
• The program learns and gets smarter with each search
• Use the "❓ Help" button anytime you're confused

🏆 SUCCESS INDICATORS:

YOU'LL KNOW IT WORKED WHEN:
   ✅ Status says "Success - Found datasheet and 3D model"
   ✅ You see filenames in the Datasheet and 3D Model boxes
   ✅ Comments area shows "Datasheet saved" and "3D model saved"
   ✅ You can find the actual files in the datasheets/ and 3d/ folders

PARTIAL SUCCESS IS ALSO GOOD:
   ⚠️ Status says "Partial success - Found datasheet only"
   ⚠️ This means you got the PDF but no 3D model
   ⚠️ This is normal - not all parts have 3D models available
"""

        basic_text.insert(tk.END, basic_help)
        basic_text.config(state='disabled')

        # Tab 2: Button Guide
        buttons_frame = ttk.Frame(notebook)
        notebook.add(buttons_frame, text="🔘 Button Guide")

        buttons_text = scrolledtext.ScrolledText(buttons_frame, wrap=tk.WORD, width=80, height=25)
        buttons_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        buttons_help = """🔘 DETAILED BUTTON GUIDE - FOR BEGINNERS

🔍 SEARCH COMPONENT BUTTON:

WHAT IT DOES:
   • Looks for datasheets and 3D models for your part
   • Downloads files to your computer automatically
   • Saves files with manufacturer name in filename

HOW TO USE IT:
   1. Type manufacturer name (like "Diodes Inc")
   2. Type part number (like "APX803L20-30SA-7")
   3. Click this button
   4. Wait and watch the comments area for progress
   5. Check the Status, Datasheet, and 3D Model fields for results

WHAT HAPPENS:
   • Progress bar starts spinning
   • Comments show "Starting search..." then detailed progress
   • Status field shows what it's doing right now
   • When done, you'll see "Success" or "Partial success" or "No files found"
   • Files are saved in datasheets/ and 3d/ folders

IF IT DOESN'T WORK:
   • Check spelling of manufacturer name
   • Try shorter manufacturer name (e.g., "Diodes" instead of "Diodes Inc")
   • If it asks for website, provide the company's main website
   • Look in comments area for specific error messages
   • Try a different part number from the same manufacturer

═══════════════════════════════════════════════════════════════════

🗑️ CLEAR RESULTS BUTTON:

WHAT IT DOES:
   • Erases all text in the big comments box at the bottom
   • Does NOT delete your files or change anything else
   • Just cleans up the screen

HOW TO USE IT:
   1. Click this button anytime
   2. The comments area becomes empty
   3. Your manufacturer/part number inputs stay the same
   4. Your downloaded files are not affected

WHEN TO USE IT:
   • When the comments area gets too cluttered
   • Before starting a new search to see results clearly
   • To clean up the screen without losing your input

IF IT DOESN'T WORK:
   • This button almost never fails
   • If comments don't clear, try clicking it again
   • Close and restart the program if needed

═══════════════════════════════════════════════════════════════════

📚 SHOW KNOWLEDGE BUTTON:

WHAT IT DOES:
   • Shows you which manufacturers the program already knows about
   • Displays how many successful searches were done for each manufacturer
   • Helps you see if your manufacturer is already "learned"

HOW TO USE IT:
   1. Click this button anytime
   2. Look in comments area for the list
   3. You'll see manufacturer names and success counts
   4. Higher success counts mean the system knows that manufacturer well

WHAT YOU'LL SEE:
   • "🏭 Diodes Inc: Base URL: https://www.diodes.com"
   • "   Success Count: 5"
   • "   Search URLs: 2, Datasheet Patterns: 1, 3D Model Patterns: 3"

WHEN TO USE IT:
   • Before searching for a new manufacturer
   • To see if the system already knows your manufacturer
   • To check how reliable the system is for different manufacturers

IF IT SHOWS NOTHING:
   • This means you haven't done any successful searches yet
   • Start with a known manufacturer like "Diodes Inc" to build knowledge
   • The system will learn as you use it

═══════════════════════════════════════════════════════════════════

📋 EDIT WEBSITES BUTTON:

WHAT IT DOES:
   • Opens a spreadsheet file that lists manufacturers and their websites
   • Lets you add new manufacturers manually
   • Shows you what manufacturers the system already knows

HOW TO USE IT:
   1. Click this button
   2. A spreadsheet file opens (usually in Excel)
   3. You can add new rows: Manufacturer Name | Website
   4. Save the file and close it
   5. The program asks if you want to reload - click Yes

WHAT THE SPREADSHEET LOOKS LIKE:
   Column A: Manufacturer Name    Column B: Website
   Diodes Inc                     https://www.diodes.com
   Texas Instruments              https://www.ti.com

HOW TO ADD A NEW MANUFACTURER:
   1. Click this button
   2. Add a new row at the bottom
   3. Type manufacturer name in Column A
   4. Type their website in Column B (like https://www.analog.com)
   5. Save and close the file
   6. Click Yes when asked to reload

IF IT DOESN'T WORK:
   • If file doesn't open, you might not have Excel installed
   • Try opening the file "actual-web-site-xref.csv" manually
   • You can edit it in Notepad if needed
   • Make sure to save as CSV format, not Excel format

═══════════════════════════════════════════════════════════════════

🧹 CLEANUP FILES BUTTON:

WHAT IT DOES:
   • Deletes old duplicate files from your computer
   • Helps keep your datasheets/ and 3d/ folders organized
   • Shows you what files it deleted

HOW TO USE IT:
   1. Click this button
   2. A dialog appears asking what to clean up
   3. Choose one option:
      - "Yes" = Clean files for current part only
      - "No" = Clean all duplicate files everywhere
      - "Cancel" = Don't clean anything

WHAT GETS DELETED:
   • Files with names like "filename_1.pdf", "filename_2.pdf"
   • Old versions of files for the same part
   • Duplicate files that accumulated over time

WHEN TO USE IT:
   • When you have too many duplicate files
   • Before doing a new search for the same part
   • Weekly cleanup to keep folders organized

IF IT DOESN'T WORK:
   • Check if files are open in another program
   • Close PDF viewers and CAD programs
   • Run as administrator if you get permission errors
   • Manually delete files if the button fails

═══════════════════════════════════════════════════════════════════

📊 VIEW LOG BUTTON:

WHAT IT DOES:
   • Shows you a history of all your searches
   • Displays what files were found and downloaded
   • Opens a detailed spreadsheet of your search results

HOW TO USE IT:
   1. Click this button
   2. Look in comments area for recent search history
   3. You'll see a summary of successful vs failed searches
   4. Click Yes when asked if you want to open the full spreadsheet

WHAT YOU'LL SEE:
   • List of all parts you've searched for
   • Which ones found datasheets (✅ PDF) and 3D models (✅ 3D)
   • Dates when you searched
   • Success rates

WHEN TO USE IT:
   • To see what parts you've already searched for
   • To check if you already have files for a part
   • To see which manufacturers work best
   • To generate reports of your component library

IF IT DOESN'T WORK:
   • If no log exists, you haven't done any searches yet
   • Do a few searches first to build up the log
   • If spreadsheet won't open, the file might be corrupted
   • Look for "found-files-log.csv" in your folder

═══════════════════════════════════════════════════════════════════

❓ HELP BUTTON:

WHAT IT DOES:
   • Opens this help window you're reading right now
   • Provides detailed instructions for everything
   • Has multiple tabs with different types of help

HOW TO USE IT:
   1. Click this button anytime you're confused
   2. Click on different tabs at the top for different topics
   3. Read the instructions for whatever you need help with
   4. Click "Close Help" when done

WHEN TO USE IT:
   • When you don't know what a button does
   • When something isn't working as expected
   • When you want to learn advanced features
   • Anytime you're stuck or confused

IF IT DOESN'T WORK:
   • This button should always work
   • If help window doesn't open, restart the program
   • All the same information is available online

═══════════════════════════════════════════════════════════════════

🚨 GENERAL TROUBLESHOOTING:

IF NOTHING WORKS:
   1. Close the program completely
   2. Restart it by running: python component_finder_gui.py
   3. Try a simple search with "Diodes Inc" and "APX803L20-30SA-7"
   4. Check that you have internet connection

IF SEARCHES ALWAYS FAIL:
   1. Try known manufacturers: Diodes Inc, Texas Instruments, Analog Devices
   2. Check manufacturer name spelling
   3. Use shorter manufacturer names
   4. Add manufacturers manually using "📋 Edit Websites"

IF FILES DON'T DOWNLOAD:
   1. Check your internet connection
   2. Make sure datasheets/ and 3d/ folders exist
   3. Check if antivirus is blocking downloads
   4. Try running as administrator

NEED MORE HELP?
   • Check the other help tabs for more detailed information
   • Look in the comments area for specific error messages
   • Try the basic examples first before complex parts
"""

        buttons_text.insert(tk.END, buttons_help)
        buttons_text.config(state='disabled')

        # Tab 3: Advanced Features
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="⚙️ Advanced Features")

        advanced_text = scrolledtext.ScrolledText(advanced_frame, wrap=tk.WORD, width=80, height=25)
        advanced_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        advanced_help = """⚙️ ADVANCED FEATURES

🧠 LEARNING SYSTEM:

The system gets smarter with each search:
• First search for a manufacturer: Discovery mode (slower)
• Subsequent searches: Uses learned patterns (faster)
• Saves successful search URLs, datasheet patterns, 3D model patterns
• Builds knowledge base in manufacturer_knowledge.json

📋 CSV DATABASE SYSTEM:

Two CSV files track your progress:

1. actual-web-site-xref.csv:
   • Maps manufacturer names to their websites
   • Prevents repeated web searches for known manufacturers
   • User-editable for adding new manufacturers
   • Automatically updated when new manufacturers are discovered

2. found-files-log.csv:
   • Complete log of all searches performed
   • Tracks success/failure rates
   • Records source URLs for all found files
   • Includes timestamps and package types
   • Perfect for analysis and reporting

🎯 SMART 3D MODEL SELECTION:

Instead of showing confusing dialogs:
• Automatically detects the part's package type (SOT23, SOIC, etc.)
• Finds the 3D model that matches the package
• Downloads only the correct model
• Ignores irrelevant models (e.g., won't download SOT25 for SOT23 part)

🧹 INTELLIGENT FILE MANAGEMENT:

• Automatic cleanup: Removes old files before downloading new ones
• No duplicates: Always keeps only the latest version
• Smart naming: Includes manufacturer name in filenames
• Organized storage: Separate folders for datasheets and 3D models

🔍 SEARCH PRIORITY SYSTEM (DISTRIBUTOR-FIRST):

1. 🥇 Search distributors FIRST for any part (NEW!)
   • Digi-Key: Simple search + part lookup + direct datasheet links
   • Mouser: Simple search + part lookup + direct datasheet links
   • Gets verified manufacturer name and official website
   • Downloads datasheets immediately if found
2. Check CSV file for known manufacturer website (if distributors fail)
3. Try partial matches in CSV (e.g., "TI" matches "Texas Instruments")
4. Search the web automatically for manufacturer website
5. Try common website patterns (manufacturer.com, etc.)
6. Ask user for website URL
7. Save successful result to CSV for future use

⚡ PERFORMANCE OPTIMIZATIONS:

• CSV lookup: Instant vs. slow web search
• Learned patterns: Skip discovery phase for known manufacturers
• Smart caching: Reuse successful search strategies
• Efficient downloads: Only get the files you need

🛠️ TROUBLESHOOTING FEATURES:

• Detailed logging: Every step is recorded in comments area
• Error handling: Clear error messages with suggested solutions
• Manual overrides: Edit CSV files when automatic detection fails
• Cleanup tools: Fix file organization issues

📊 ANALYTICS & REPORTING:

• Success rate tracking by manufacturer
• Search history with timestamps
• File source tracking for re-downloading
• Export capabilities for team sharing

🔧 CUSTOMIZATION OPTIONS:

• Edit manufacturer websites manually
• Add new manufacturers to CSV
• Customize file naming patterns
• Adjust search strategies per manufacturer
"""

        advanced_text.insert(tk.END, advanced_help)
        advanced_text.config(state='disabled')

        # Tab 4: Complete Button Reference
        buttons_ref_frame = ttk.Frame(notebook)
        notebook.add(buttons_ref_frame, text="🔘 All Buttons")

        buttons_ref_text = scrolledtext.ScrolledText(buttons_ref_frame, wrap=tk.WORD, width=80, height=25)
        buttons_ref_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        buttons_ref_help = """🔘 COMPLETE BUTTON REFERENCE

═══════════════════════════════════════════════════════════════════
🔍 SEARCH COMPONENT BUTTON
═══════════════════════════════════════════════════════════════════

LOCATION: Top row, first button
PURPOSE: Main search function - finds datasheets and 3D models

STEP-BY-STEP USAGE:
1. Enter manufacturer name in first text box
2. Enter part number in second text box
3. Click this button
4. Watch progress bar and comments area
5. Check status fields for results

WHAT IT DOES INTERNALLY:
• Checks if manufacturer is known (CSV lookup)
• If unknown, tries to find manufacturer website
• Searches manufacturer website for the part
• Downloads datasheet PDF if found
• Downloads correct 3D model if found
• Saves files with manufacturer name in filename
• Logs everything to found-files-log.csv

TROUBLESHOOTING:
❌ "Could not find manufacturer website"
   → Program automatically searches the web first (NEW!)
   → Click "📋 Edit Websites" and add the manufacturer manually
   → Try shorter manufacturer name (e.g., "TI" instead of "Texas Instruments")

❌ "No part found on website"
   → Check part number spelling
   → Try searching manufacturer website manually first
   → Some parts may not be available online

❌ "No datasheet found"
   → Part page exists but no PDF download link
   → May need manual assistance (see Troubleshooting tab)

❌ "No 3D model found"
   → Not all parts have 3D models available
   → Check if manufacturer provides CAD models for this part family

═══════════════════════════════════════════════════════════════════
🗑️ CLEAR RESULTS BUTTON
═══════════════════════════════════════════════════════════════════

LOCATION: Top row, second button
PURPOSE: Cleans up the display without affecting your work

WHAT IT DOES:
• Clears all text from the comments area (big text box at bottom)
• Does NOT clear your input fields (manufacturer/part number)
• Does NOT clear status fields (Status/Datasheet/3D Model)
• Does NOT delete any downloaded files
• Does NOT affect the program's memory or knowledge base

WHEN TO USE:
• Before starting a new search to see results clearly
• When comments area gets too cluttered to read
• To clean up display without losing your input data

NEVER FAILS: This button should always work instantly

═══════════════════════════════════════════════════════════════════
📚 SHOW KNOWLEDGE BUTTON
═══════════════════════════════════════════════════════════════════

LOCATION: Top row, third button
PURPOSE: Shows what manufacturers the program has learned

WHAT YOU'LL SEE:
🏭 Diodes Inc:
   Base URL: https://www.diodes.com
   Success Count: 5
   Search URLs: 2
   Datasheet Patterns: 1
   3D Model Patterns: 3

INTERPRETATION:
• Success Count: How many successful searches for this manufacturer
• Higher numbers = more reliable for future searches
• Search URLs: Different ways the program can search this manufacturer
• Patterns: Learned methods for finding datasheets and 3D models

WHEN TO USE:
• Before searching a new manufacturer (to see if it's known)
• To check reliability of different manufacturers
• To see how much the program has learned

IF EMPTY:
• You haven't done any successful searches yet
• Try the example: Diodes Inc + APX803L20-30SA-7
• Knowledge builds up as you use the program

═══════════════════════════════════════════════════════════════════
📋 EDIT WEBSITES BUTTON
═══════════════════════════════════════════════════════════════════

LOCATION: Top row, fourth button
PURPOSE: Manually add/edit manufacturer websites

WHAT HAPPENS:
1. Shows current CSV contents in comments area
2. Opens actual-web-site-xref.csv in Excel (or default program)
3. You can edit the spreadsheet
4. Asks if you want to reload when done

CSV FORMAT:
Column A: Manufacturer Name    Column B: Website
Diodes Inc                     https://www.diodes.com
Texas Instruments              https://www.ti.com

HOW TO ADD NEW MANUFACTURER:
1. Click this button
2. Add new row at bottom of spreadsheet
3. Type manufacturer name in Column A
4. Type website URL in Column B (must start with https://)
5. Save file and close Excel
6. Click "Yes" when asked to reload

COMMON PROBLEMS:
❌ File won't open
   → You might not have Excel installed
   → Try opening "actual-web-site-xref.csv" manually
   → Can edit in Notepad if needed

❌ Changes don't take effect
   → Make sure you saved the file
   → Click "Yes" when asked to reload
   → Check that website URL starts with https://

═══════════════════════════════════════════════════════════════════
🧹 CLEANUP FILES BUTTON
═══════════════════════════════════════════════════════════════════

LOCATION: Top row, fifth button
PURPOSE: Removes duplicate and old files

DIALOG OPTIONS:
• "Yes" = Clean files for current part only
• "No" = Clean all duplicate files everywhere
• "Cancel" = Don't clean anything

WHAT GETS DELETED:
✅ Files with _1, _2, _3 suffixes (duplicates)
✅ Old versions of files for the same part
✅ Files that accumulated from multiple downloads

WHAT NEVER GETS DELETED:
❌ The latest version of each file
❌ Files from different parts
❌ Files from different manufacturers

WHEN TO USE:
• Weekly cleanup to keep folders organized
• Before searching for a part you've searched before
• When you have too many duplicate files

SAFETY FEATURES:
• Shows you what it will delete before doing it
• Only deletes files it created (with specific naming pattern)
• Never deletes files you created manually

═══════════════════════════════════════════════════════════════════
📊 VIEW LOG BUTTON
═══════════════════════════════════════════════════════════════════

LOCATION: Top row, sixth button
PURPOSE: Shows history of all your searches

WHAT YOU'LL SEE:
Manufacturer     | Part Number     | ✅ PDF | ✅ 3D | Package | Date     | Success
Diodes Inc       | APX803L20-30SA-7| ✅ PDF | ✅ 3D | SOT23   | 2024-01-15| Success
Texas Instru...  | LM358           | ✅ PDF | ❌ 3D | DIP     | 2024-01-15| Partial

SYMBOLS MEANING:
✅ = File was found and downloaded
❌ = File was not found
Success = Both datasheet and 3D model found
Partial = Only datasheet OR only 3D model found
Failed = Neither datasheet nor 3D model found

FULL SPREADSHEET:
• Click "Yes" when asked to open full CSV
• Contains complete details: URLs, filenames, timestamps
• Can be opened in Excel for analysis
• Perfect for generating reports

WHEN TO USE:
• To see what parts you've already searched
• To check success rates by manufacturer
• To find source URLs for re-downloading
• To generate component library reports

═══════════════════════════════════════════════════════════════════
❓ HELP BUTTON
═══════════════════════════════════════════════════════════════════

LOCATION: Top row, seventh button
PURPOSE: Opens this help system

HELP TABS:
🚀 Basic Usage: Step-by-step beginner guide
🔘 Button Guide: Detailed explanation of each button (previous tab)
⚙️ Advanced Features: Technical details and advanced usage
📁 File Organization: Where files go and how they're named
🔘 All Buttons: Complete reference (this tab)
🛠️ Manual Help: How to help the program when it fails

WHEN TO USE:
• Anytime you're confused about what a button does
• When something isn't working as expected
• To learn advanced features
• Before trying something new

NAVIGATION:
• Click tabs at top to switch topics
• Scroll up/down within each tab
• Click "Close Help" when finished

═══════════════════════════════════════════════════════════════════
📋 FOUND FILES BUTTON
═══════════════════════════════════════════════════════════════════

LOCATION: Top row, eighth button (last button)
PURPOSE: Directly opens the found files CSV log

WHAT IT DOES:
• Opens found-files-log.csv in your default spreadsheet program (Excel)
• Shows complete history of all searches with full details
• No preview in comments area - goes straight to the file

DIFFERENCE FROM "📊 View Log":
📊 View Log: Shows summary in comments area first, then asks if you want to open CSV
📋 Found Files: Opens the CSV file immediately without preview

CSV CONTAINS:
• Manufacturer Name - What you entered
• Part Number - What you searched for
• Datasheet URL - Where datasheet was found
• Datasheet Filename - Local file saved
• 3D Model URL - Where 3D model was found
• 3D Model Filename - Local file saved
• Package Type - Detected package (SOT23, etc.)
• Date Found - When search was performed
• Search Success - Success/Partial/Failed status

WHEN TO USE:
• When you want detailed analysis in Excel
• To check complete search history quickly
• To find source URLs for re-downloading files
• To generate reports or share findings
• To verify what files you have vs. what you've searched for

TROUBLESHOOTING:
❌ File won't open
   → You might not have Excel or a CSV viewer installed
   → Try opening "found-files-log.csv" manually from the folder
   → Can be opened in Notepad if needed

❌ File is empty
   → You haven't done any searches yet
   → Do a few searches first to populate the log
   → Check that searches are completing successfully

❌ File shows old data
   → The CSV updates automatically after each search
   → Try refreshing/reopening the file
   → Check the Date Found column for recent entries

QUICK ACCESS: This button provides the fastest way to get to your complete search data!
"""

        buttons_ref_text.insert(tk.END, buttons_ref_help)
        buttons_ref_text.config(state='disabled')

        # Tab 5: Manual Help/Troubleshooting
        manual_help_frame = ttk.Frame(notebook)
        notebook.add(manual_help_frame, text="🛠️ Manual Help")

        manual_help_text = scrolledtext.ScrolledText(manual_help_frame, wrap=tk.WORD, width=80, height=25)
        manual_help_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        manual_help_content = """🛠️ MANUAL HELP - WHEN THE PROGRAM CAN'T FIND FILES

═══════════════════════════════════════════════════════════════════
🚨 WHEN AUTOMATIC SEARCH FAILS
═══════════════════════════════════════════════════════════════════

IF THE PROGRAM SAYS "NO FILES FOUND" OR "PARTIAL SUCCESS":
You need to help the program learn where to find the files.
This is normal - not all manufacturer websites work the same way.

STEP 1: UNDERSTAND WHAT FAILED
Look at the Status field:
• "❌ No files found" = Couldn't find datasheet OR 3D model
• "⚠️ Partial success - Found datasheet only" = Found PDF but no 3D model
• "⚠️ Partial success - Found 3D model only" = Found 3D but no PDF

STEP 2: CHECK THE COMMENTS AREA
Look for specific error messages:
• "Could not find manufacturer website" → Need to add website manually
• "No part found on website" → Part number or search method issue
• "No datasheet found" → Datasheet exists but program can't find download link
• "No 3D model found" → 3D model may not exist or is in different location

═══════════════════════════════════════════════════════════════════
🔧 HELPING THE PROGRAM FIND DATASHEETS
═══════════════════════════════════════════════════════════════════

MANUAL DATASHEET HUNTING:

STEP 1: FIND THE PART MANUALLY
1. Open your web browser
2. Go to the manufacturer's website
3. Use their search function to find your part
4. Navigate to the part's detail page

STEP 2: LOCATE THE DATASHEET
Look for these common labels:
• "Datasheet"
• "Data Sheet"
• "Product Brief"
• "Technical Documentation"
• "PDF Download"
• "Specifications"

STEP 3: GET THE DIRECT LINK
1. Right-click on the datasheet link
2. Select "Copy Link Address" or "Copy Link Location"
3. This gives you the direct URL to the PDF file

STEP 4: HELP THE PROGRAM LEARN
Unfortunately, the current version doesn't have a way to manually input URLs.
Future versions will include this feature. For now:
1. Download the file manually
2. Rename it to match the program's format:
   "ManufacturerName PartNumber_datasheet.pdf"
3. Put it in the datasheets/ folder

EXAMPLE MANUAL FILENAME:
Original: "APX803L.pdf"
Rename to: "Diodes_Inc APX803L20-30SA-7_datasheet.pdf"

═══════════════════════════════════════════════════════════════════
🎯 HELPING THE PROGRAM FIND 3D MODELS
═══════════════════════════════════════════════════════════════════

MANUAL 3D MODEL HUNTING:

STEP 1: UNDERSTAND 3D MODEL TYPES
• STEP files (.step, .stp) - Universal 3D format
• IGES files (.iges, .igs) - Older 3D format
• Native CAD files (.dwg, .sldprt) - Specific to CAD software

STEP 2: COMMON LOCATIONS ON MANUFACTURER WEBSITES
Look for these sections:
• "CAD Models"
• "3D Models"
• "Design Resources"
• "CAD Downloads"
• "Mechanical Drawings"
• "Package Information"

STEP 3: PACKAGE-SPECIFIC MODELS
3D models are usually organized by package type:
• SOT23 parts → Look for SOT23.step
• SOIC parts → Look for SOIC8.step or SOIC14.step
• QFN parts → Look for QFN32.step

STEP 4: DOWNLOAD THE CORRECT MODEL
1. Find the section with 3D models
2. Look for your part's package type (SOT23, SOIC, etc.)
3. Download the STEP file for that package
4. Rename it to match program format:
   "ManufacturerName PartNumber_PackageType.step"

EXAMPLE MANUAL 3D MODEL:
Original: "SOT23.stp"
Rename to: "Diodes_Inc APX803L20-30SA-7_SOT23.step"
Put in: 3d/ folder

═══════════════════════════════════════════════════════════════════
🤝 WORKING WITH THE PROGRAM TO IMPROVE IT
═══════════════════════════════════════════════════════════════════

TEACHING THE PROGRAM NEW MANUFACTURERS:

STEP 1: ADD MANUFACTURER TO CSV
1. Click "📋 Edit Websites" button
2. Add new row: Manufacturer Name | Website URL
3. Save and reload

STEP 2: TEST THE SEARCH
1. Try searching for a part from that manufacturer
2. If it fails, note the specific error messages
3. Try different part numbers from the same manufacturer

STEP 3: MANUAL WEBSITE ANALYSIS
If the program can't find parts automatically:
1. Go to the manufacturer website manually
2. Try their search function with your part number
3. Note the URL pattern of the search results
4. Note the URL pattern of the part detail pages
5. Note where datasheets and 3D models are located

STEP 4: DOCUMENT YOUR FINDINGS
Keep notes on:
• Which manufacturers work automatically
• Which ones need manual help
• Common patterns you notice
• Successful part numbers for testing

═══════════════════════════════════════════════════════════════════
🔍 ADVANCED TROUBLESHOOTING TECHNIQUES
═══════════════════════════════════════════════════════════════════

WHEN SEARCHES CONSISTENTLY FAIL:

CHECK MANUFACTURER NAME VARIATIONS:
Try these different formats:
• "Texas Instruments" vs "TI"
• "STMicroelectronics" vs "ST"
• "Analog Devices" vs "ADI"
• "ON Semiconductor" vs "ON Semi"

CHECK PART NUMBER VARIATIONS:
Try these formats:
• With/without hyphens: "APX803L20-30SA-7" vs "APX803L2030SA7"
• With/without spaces: "LM 358" vs "LM358"
• Different suffixes: "LM358N" vs "LM358" vs "LM358P"

VERIFY PART EXISTS:
1. Search Google for: "manufacturer part_number datasheet"
2. Check if the part is current (not obsolete)
3. Verify you have the correct part number format

NETWORK AND PERMISSION ISSUES:
• Check internet connection
• Try running as administrator
• Check if antivirus is blocking downloads
• Verify firewall isn't blocking the program

═══════════════════════════════════════════════════════════════════
📞 WHEN ALL ELSE FAILS
═══════════════════════════════════════════════════════════════════

FALLBACK STRATEGIES:

1. MANUAL DOWNLOAD AND ORGANIZATION:
   • Download files manually from manufacturer websites
   • Use the program's naming convention
   • Put files in correct folders (datasheets/, 3d/)
   • The program will recognize them in future searches

2. FOCUS ON KNOWN WORKING MANUFACTURERS:
   • Build up your library with manufacturers that work well
   • Use "📚 Show Knowledge" to see which ones are reliable
   • Gradually expand to new manufacturers

3. PARTIAL SUCCESS IS STILL SUCCESS:
   • Getting just the datasheet is valuable
   • 3D models aren't always available anyway
   • Focus on collecting datasheets first, 3D models second

4. COMMUNITY RESOURCES:
   • Many 3D models are available on sites like GrabCAD
   • Component databases like Octopart can help find datasheets
   • Manufacturer forums often have additional resources

REMEMBER: The program learns and improves with each successful search.
Even partial successes help it get better at finding files automatically.
"""

        manual_help_text.insert(tk.END, manual_help_content)
        manual_help_text.config(state='disabled')

        # Tab 6: File Organization
        files_frame = ttk.Frame(notebook)
        notebook.add(files_frame, text="📁 File Organization")

        files_text = scrolledtext.ScrolledText(files_frame, wrap=tk.WORD, width=80, height=25)
        files_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        files_help = """📁 FILE ORGANIZATION SYSTEM

📂 DIRECTORY STRUCTURE:

Your workspace (e:\\python\\web-get-files) contains these important directories and files:

📁 datasheets/
   • All PDF datasheets are saved here
   • Filenames include manufacturer name
   • Example: "Diodes_Inc APX803L20-30SA-7_datasheet.pdf"
   • Automatically cleaned of duplicates

📁 3d/
   • All STEP 3D models are saved here
   • Filenames include manufacturer name and package type
   • Example: "Diodes_Inc APX803L20-30SA-7_SOT23.step"
   • Only the correct model for the package is saved

📄 actual-web-site-xref.csv
   • Maps manufacturer names to their websites
   • Two columns: "Manufacturer Name", "Website"
   • User-editable in Excel or any text editor
   • Automatically updated when new manufacturers are found

📄 found-files-log.csv
   • Complete log of all search activities
   • Nine columns tracking every aspect of each search
   • Includes URLs, filenames, timestamps, success status
   • Perfect for analysis and reporting

📄 manufacturer_knowledge.json
   • Technical knowledge base (JSON format)
   • Stores learned search patterns and strategies
   • Automatically maintained by the system
   • Makes future searches faster and more reliable

🏷️ FILENAME CONVENTIONS:

Datasheets:
   Format: "{Manufacturer} {PartNumber}_datasheet.pdf"
   Examples:
   • "Diodes_Inc APX803L20-30SA-7_datasheet.pdf"
   • "Texas_Instruments LM358_datasheet.pdf"
   • "Analog_Devices AD8065_datasheet.pdf"

3D Models:
   Format: "{Manufacturer} {PartNumber}_{Package}.step"
   Examples:
   • "Diodes_Inc APX803L20-30SA-7_SOT23.step"
   • "Texas_Instruments LM358_DIP.step"
   • "Analog_Devices AD8065_SOIC.step"

🧹 FILE MANAGEMENT:

Automatic Cleanup:
   • Before each download, old files for the same part are deleted
   • Prevents accumulation of duplicate files
   • Ensures you always have the latest version

Manual Cleanup Options:
   • Current Part: Remove files for currently entered part
   • All Duplicates: Remove all files with _1, _2, etc. suffixes
   • Accessible via "🧹 Cleanup Files" button

📊 FILE TRACKING:

Every file download is logged with:
   • Source URL where file was found
   • Local filename where file was saved
   • Timestamp when file was downloaded
   • Success/failure status
   • Package type and other metadata

💡 ORGANIZATION BENEFITS:

• Easy Identification: Manufacturer name in filename
• No Conflicts: Each manufacturer's files are clearly separated
• Version Control: Always have the latest version
• Audit Trail: Complete history of where files came from
• Team Sharing: Consistent naming for collaboration

🔍 FINDING YOUR FILES:

• All files are in clearly named directories
• Filenames include all key information
• CSV logs help you find files later
• Consistent naming makes sorting and searching easy

🛠️ MAINTENANCE:

• Use "🧹 Cleanup Files" regularly to remove duplicates
• Check "📊 View Log" to see what files you have
• Edit CSV files to add new manufacturers
• Monitor directory sizes to manage disk space
"""

        files_text.insert(tk.END, files_help)
        files_text.config(state='disabled')

        # Close button
        close_button = ttk.Button(help_window, text="Close Help", command=help_window.destroy)
        close_button.pack(pady=10)

        # Set focus to help window
        help_window.focus_set()
    
    def start_search(self):
        """Start the component search in a separate thread"""
        manufacturer = self.manufacturer_var.get().strip()
        part_number = self.part_number_var.get().strip()
        
        if not manufacturer or not part_number:
            messagebox.showerror("Error", "Please enter both manufacturer and part number")
            return
        
        # Disable search button and start progress
        self.search_button.config(state='disabled')
        self.progress.start()
        self.status_var.set("Searching...")
        
        # Start search in separate thread
        search_thread = threading.Thread(target=self.search_component, args=(manufacturer, part_number))
        search_thread.daemon = True
        search_thread.start()
    
    def search_component(self, manufacturer, part_number):
        """Search for component (runs in separate thread)"""
        try:
            self.add_comment(f"🔍 Starting search for {manufacturer} {part_number}")
            self.status_text.set("Starting search...")
            self.pdf_file_text.set("")
            self.step_file_text.set("")

            # Step 0: ALWAYS try distributors first for any part
            self.add_comment(f"📊 Checking distributors first for part {part_number}...")
            distributor_info = self.search_distributors_for_part_info(manufacturer, part_number)
            if distributor_info:
                verified_manufacturer, website, datasheet_url = distributor_info
                self.add_comment(f"✅ Distributor verified: {verified_manufacturer} → {website}")

                # If we found a direct datasheet link, download it
                if datasheet_url:
                    self.add_comment(f"📄 Found direct datasheet link: {datasheet_url}")
                    self.download_file_from_url(datasheet_url, verified_manufacturer, part_number, "datasheet")

                # Update our search with verified info
                manufacturer = verified_manufacturer
                manufacturer_key = manufacturer.lower().strip()

                # Save to knowledge base and CSV
                self._save_manufacturer_to_csv(verified_manufacturer, website)
                if verified_manufacturer.lower() != manufacturer.lower():
                    self._save_manufacturer_to_csv(manufacturer, website)

                # Continue to search manufacturer website for STEP files
                self.add_comment(f"🌐 Using verified website: {website}")

                # For WURTH, use simple search method
                if 'wurth' in manufacturer.lower() or 'würth' in manufacturer.lower():
                    self.add_comment(f"🔍 Trying simple WURTH website search for {manufacturer}...")
                    self.search_wurth_simple(manufacturer, part_number)
                else:
                    self.add_comment(f"🔍 Using complex search for {manufacturer}...")
                    self.search_with_known_website(manufacturer, part_number, website)
                return

            # Step 1: Check for known parts first
            manufacturer_key = manufacturer.lower().strip()
            self.add_comment(f"🌐 Distributors didn't find part, trying manufacturer website...")

            # Check if we have this specific part in our knowledge base
            if manufacturer_key in self.knowledge_base:
                known_parts = self.knowledge_base[manufacturer_key].get("known_parts", {})
                if part_number in known_parts:
                    self.add_comment(f"✅ Found {part_number} in known parts database!")
                    part_info = known_parts[part_number]

                    # Download datasheet if available
                    if "datasheet" in part_info:
                        datasheet_url = part_info["datasheet"]
                        self.add_comment(f"📄 Found known datasheet: {datasheet_url}")
                        self.download_file_from_url(datasheet_url, manufacturer, part_number, "datasheet")

                    # Download STEP file if available
                    if "step_file" in part_info:
                        step_url = part_info["step_file"]
                        self.add_comment(f"🔧 Found known STEP file: {step_url}")
                        self.download_file_from_url(step_url, manufacturer, part_number, "3d_model")

                    # Show description if available
                    if "description" in part_info:
                        self.add_comment(f"📋 Part description: {part_info['description']}")

                    self.add_comment(f"✅ Successfully found files for {part_number}")
                    return

            # Step 2: Try simple manufacturer website search (WURTH only)
            if 'wurth' in manufacturer.lower():
                self.add_comment(f"🔍 Trying simple WURTH website search...")
                success = self.search_wurth_simple(manufacturer, part_number)
                if success:
                    return
                else:
                    self.add_comment(f"✅ Completed WURTH search")
                    return  # Don't try other complex methods

            # Step 3: Try other manufacturer searches
            if manufacturer_key in self.knowledge_base:
                self.add_comment(f"✅ Found {manufacturer} in knowledge base")
                self.status_text.set("Using learned patterns...")
                website_url = self.knowledge_base[manufacturer_key]["base_url"]
            else:
                self.add_comment(f"🆕 {manufacturer} not in knowledge base")
                self.status_text.set("Finding manufacturer website...")
                website_url = self.find_manufacturer_website(manufacturer)
                
                if not website_url:
                    # Ask user for website
                    self.root.after(0, self.ask_for_website, manufacturer)
                    return
            
            self.add_comment(f"🌐 Using website: {website_url}")
            
            # Step 2: Search for the component
            self.search_with_known_website(manufacturer, part_number, website_url)
            
        except Exception as e:
            self.add_comment(f"❌ Search failed: {e}")
            logger.error(f"Search error: {e}")
        finally:
            # Re-enable UI
            self.root.after(0, self.search_complete)

    def search_distributors_for_part_info(self, manufacturer, part_number):
        """Search distributors (Digi-Key, Mouser) for part info - SIMPLE METHOD ONLY"""

        # Try simple Digi-Key search first (like pasting in search box)
        simple_digikey_result = self.search_digikey_simple(manufacturer, part_number)
        if simple_digikey_result:
            return simple_digikey_result

        # Try simple Mouser search (like pasting in search box)
        simple_mouser_result = self.search_mouser_simple(manufacturer, part_number)
        if simple_mouser_result:
            return simple_mouser_result

        return None

    def search_digikey_simple(self, manufacturer, part_number):
        """Search Digi-Key exactly like typing part number in their search box"""
        try:
            self.add_comment(f"🔍 Searching Digi-Key website for {part_number}...")

            # This is exactly what happens when you type in Digi-Key's search box
            search_url = f"https://www.digikey.com/en/products/result?keywords={part_number}"

            import time
            time.sleep(1)

            self.add_comment(f"   Opening: {search_url}")
            response = self.session.get(search_url, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()

                # Check if our part number appears on the page
                if part_number.lower() in page_text:
                    self.add_comment(f"✅ Found {part_number} on Digi-Key")

                    # Look for ANY manufacturer (not just WURTH)
                    manufacturer_found = None
                    manufacturer_lower = manufacturer.lower()

                    # Check if the expected manufacturer appears on the page
                    if manufacturer_lower in page_text:
                        manufacturer_found = manufacturer
                        self.add_comment(f"✅ Found {manufacturer} manufacturer on Digi-Key")
                    else:
                        # If expected manufacturer not found, still try to get datasheet
                        manufacturer_found = manufacturer  # Use the one from Excel anyway
                        self.add_comment(f"⚠️ {manufacturer} not explicitly found, but part exists on Digi-Key")

                    # Look for datasheet ONLY on Digi-Key (STEP files are on manufacturer website)
                    datasheet_url = None

                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        link_text = link.get_text().lower()

                        # Make URL absolute
                        if href.startswith('http'):
                            full_url = href
                        elif href.startswith('/'):
                            full_url = urljoin('https://www.digikey.com', href)
                        else:
                            continue

                        # Look for datasheet links
                        if any(keyword in link_text for keyword in ['datasheet', 'pdf', 'data sheet']):
                            datasheet_url = full_url
                            self.add_comment(f"📄 Found datasheet: {datasheet_url[:60]}...")
                            break  # Take first datasheet found

                    # Return the result with the manufacturer from Excel (not hardcoded)
                    if datasheet_url and manufacturer_found:
                        return (manufacturer_found, "https://www.digikey.com", datasheet_url)

                        return ("Würth Elektronik", "https://www.we-online.com", datasheet_url)

                self.add_comment(f"   Part not found on Digi-Key")
                return None

            elif response.status_code == 429:
                self.add_comment(f"   ⚠️ Rate limited by Digi-Key")
                return None
            else:
                self.add_comment(f"   ❌ HTTP {response.status_code}")
                return None

        except Exception as e:
            self.add_comment(f"⚠️ Digi-Key search failed: {str(e)[:50]}")
            return None

        except Exception as e:
            self.add_comment(f"⚠️ Simple Digi-Key search failed: {str(e)[:50]}")
            import traceback
            self.add_comment(f"   Debug: {traceback.format_exc()[:100]}...")
            return None



    def search_digikey_for_part_info(self, manufacturer, part_number):
        """Search Digi-Key for specific part to get verified manufacturer info and datasheet"""
        try:
            self.add_comment(f"🔍 Searching Digi-Key for part {part_number}...")

            # Add delay to be respectful to Digi-Key
            import time
            time.sleep(2)

            # Use Digi-Key's main search interface (less likely to be rate limited)
            search_urls = [
                f"https://www.digikey.com/en/products/result?keywords={quote(part_number)}&stock=1",
                f"https://www.digikey.com/products/en?keywords={quote(part_number)}",
                f"https://www.digikey.com/en/products/filter?keywords={quote(part_number)}&stock=1"
            ]

            for search_url in search_urls:
                try:
                    self.add_comment(f"   Trying: {search_url[:50]}...")
                    response = self.session.get(search_url, timeout=20)

                    if response.status_code == 429:
                        self.add_comment("   ⚠️ Rate limited by Digi-Key, trying Mouser instead...")
                        return None
                    elif response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Look for the part in search results
                        for result_row in soup.find_all(['tr', 'div'], class_=re.compile(r'product|part|result', re.I)):

                            # Check if this row contains our part number
                            row_text = result_row.get_text()
                            if part_number.lower() in row_text.lower():

                                # Look for manufacturer information and datasheet link
                                part_info = self._extract_part_info_from_digikey_result(result_row, manufacturer, part_number)
                                if part_info:
                                    official_name, website, datasheet_url = part_info
                                    self.add_comment(f"✅ Found part on Digi-Key: {official_name}")
                                    if datasheet_url:
                                        self.add_comment(f"📄 Found datasheet link: {datasheet_url[:60]}...")
                                    return official_name, website, datasheet_url

                        # If no exact match, look for similar parts from the same manufacturer
                        self.add_comment(f"   No exact match, looking for {manufacturer} parts...")

                        for result_row in soup.find_all(['tr', 'div'], class_=re.compile(r'product|part|result', re.I)):
                            row_text = result_row.get_text().lower()

                            # Check if this row mentions our manufacturer
                            if any(word in row_text for word in manufacturer.lower().split() if len(word) > 2):
                                part_info = self._extract_part_info_from_digikey_result(result_row, manufacturer, part_number)
                                if part_info:
                                    official_name, website, datasheet_url = part_info
                                    self.add_comment(f"✅ Found manufacturer on Digi-Key: {official_name}")
                                    return official_name, website, datasheet_url

                    # Small delay between attempts
                    time.sleep(1)

                except Exception as e:
                    self.add_comment(f"   ❌ Failed {search_url[:30]}: {str(e)[:30]}")
                    continue

            return None

        except Exception as e:
            self.add_comment(f"⚠️ Digi-Key search failed: {str(e)[:50]}")
            return None

    def search_mouser_simple(self, manufacturer, part_number):
        """Search Mouser exactly like typing part number in their search box"""
        try:
            self.add_comment(f"🔍 Searching Mouser website for {part_number}...")

            # This is exactly what happens when you type in Mouser's search box
            search_url = f"https://www.mouser.com/c/?q={part_number}"

            import time
            time.sleep(1)

            self.add_comment(f"   Opening: {search_url}")
            response = self.session.get(search_url, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()

                # Check if our part number appears on the page
                if part_number.lower() in page_text:
                    self.add_comment(f"✅ Found {part_number} on Mouser")

                    # Look for ANY manufacturer (not just WURTH)
                    manufacturer_found = None
                    manufacturer_lower = manufacturer.lower()

                    # Check if the expected manufacturer appears on the page
                    if manufacturer_lower in page_text:
                        manufacturer_found = manufacturer
                        self.add_comment(f"✅ Found {manufacturer} manufacturer on Mouser")
                    else:
                        # If expected manufacturer not found, still try to get datasheet
                        manufacturer_found = manufacturer  # Use the one from Excel anyway
                        self.add_comment(f"⚠️ {manufacturer} not explicitly found, but part exists on Mouser")

                    # Look for datasheet ONLY on Mouser (STEP files are on manufacturer website)
                    datasheet_url = None

                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        link_text = link.get_text().lower()

                        # Make URL absolute
                        if href.startswith('http'):
                            full_url = href
                        elif href.startswith('/'):
                            full_url = urljoin('https://www.mouser.com', href)
                        else:
                            continue

                        # Look for datasheet links
                        if any(keyword in link_text for keyword in ['datasheet', 'pdf', 'data sheet']):
                            datasheet_url = full_url
                            self.add_comment(f"📄 Found datasheet: {datasheet_url[:60]}...")
                            break  # Take first datasheet found

                    # Return the result with the manufacturer from Excel (not hardcoded)
                    if datasheet_url and manufacturer_found:
                        return (manufacturer_found, "https://www.mouser.com", datasheet_url)
                        if datasheet_url:
                            self.add_comment(f"📥 Downloading datasheet from Mouser...")
                            self.download_file_from_url(datasheet_url, "Würth Elektronik", part_number, "datasheet")

                        return ("Würth Elektronik", "https://www.we-online.com", datasheet_url)

                self.add_comment(f"   Part not found on Mouser")
                return None

            elif response.status_code == 429:
                self.add_comment(f"   ⚠️ Rate limited by Mouser")
                return None
            else:
                self.add_comment(f"   ❌ HTTP {response.status_code}")
                return None

        except Exception as e:
            self.add_comment(f"⚠️ Mouser search failed: {str(e)[:50]}")
            return None



    def search_mouser_for_part_info(self, manufacturer, part_number):
        """Search Mouser for specific part to get verified manufacturer info and datasheet"""
        try:
            self.add_comment(f"🔍 Searching Mouser for part {part_number}...")

            # Add delay to be respectful
            import time
            time.sleep(1)

            # Try multiple Mouser search approaches
            search_urls = [
                f"https://www.mouser.com/ProductDetail/?q={quote(part_number)}",
                f"https://www.mouser.com/c/?q={quote(part_number)}",
                f"https://www.mouser.com/ProductDetail/{quote(manufacturer)}/{quote(part_number)}/",
                f"https://www.mouser.com/c/connectors/?q={quote(part_number)}"  # Since WURTH makes connectors
            ]

            for search_url in search_urls:
                try:
                    self.add_comment(f"   Trying: {search_url[:50]}...")
                    response = self.session.get(search_url, timeout=20)

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Look for manufacturer information and datasheet links
                        part_info = self._extract_part_info_from_mouser_page(soup, manufacturer, part_number)
                        if part_info:
                            official_name, website, datasheet_url = part_info
                            self.add_comment(f"✅ Found part on Mouser: {official_name}")
                            if datasheet_url:
                                self.add_comment(f"📄 Found datasheet link: {datasheet_url[:60]}...")
                            return official_name, website, datasheet_url

                        # Also look for search results in the page
                        for result in soup.find_all(['div', 'tr', 'td'], class_=re.compile(r'search-result|product|part', re.I)):
                            result_text = result.get_text()
                            if part_number.lower() in result_text.lower():
                                part_info = self._extract_part_info_from_mouser_result(result, manufacturer, part_number)
                                if part_info:
                                    official_name, website, datasheet_url = part_info
                                    self.add_comment(f"✅ Found part on Mouser: {official_name}")
                                    return official_name, website, datasheet_url

                    # Small delay between attempts
                    time.sleep(1)

                except Exception as e:
                    self.add_comment(f"   ❌ Failed {search_url[:30]}: {str(e)[:30]}")
                    continue

            return None

        except Exception as e:
            self.add_comment(f"⚠️ Mouser search failed: {str(e)[:50]}")
            return None

    def _extract_part_info_from_digikey_result(self, result_element, manufacturer, part_number):
        """Extract manufacturer name, website, and datasheet link from Digi-Key result"""
        try:
            official_name = None
            website = None
            datasheet_url = None

            # Look for datasheet links first
            for link in result_element.find_all('a', href=True):
                href = link['href']
                link_text = link.get_text().strip().lower()

                # Check for datasheet links
                if any(keyword in link_text for keyword in ['datasheet', 'data sheet', 'pdf', 'specification']):
                    if href.startswith('http'):
                        datasheet_url = href
                        self.add_comment(f"   Found datasheet link: {href[:50]}...")
                    elif href.startswith('/'):
                        datasheet_url = urljoin('https://www.digikey.com', href)
                        self.add_comment(f"   Found datasheet link: {datasheet_url[:50]}...")

                # Check for manufacturer website links
                elif href.startswith('http') and 'digikey.com' not in href:
                    if any(word in href.lower() or word in link_text
                          for word in manufacturer.lower().split() if len(word) > 2):
                        website = href
                        official_name = link_text.title() or manufacturer

            # Look for manufacturer name in the result text
            result_text = result_element.get_text()

            # Common patterns for manufacturer names
            mfg_patterns = [
                r'Manufacturer[:\s]+([^,\n\|]+)',
                r'Mfg[:\s]+([^,\n\|]+)',
                r'Brand[:\s]+([^,\n\|]+)',
                r'by\s+([^,\n\|]+)',
            ]

            for pattern in mfg_patterns:
                match = re.search(pattern, result_text, re.I)
                if match:
                    found_mfg = match.group(1).strip()
                    # Clean up the manufacturer name
                    found_mfg = re.sub(r'\s+', ' ', found_mfg)  # Clean whitespace
                    found_mfg = found_mfg.split('|')[0].strip()  # Remove extra info after |

                    if len(found_mfg) > 2 and not official_name:
                        official_name = found_mfg

                        # Try to construct website if we don't have one
                        if not website:
                            website_guess = f"https://www.{found_mfg.lower().replace(' ', '').replace('-', '')}.com"
                            if self._test_manufacturer_website(website_guess, manufacturer):
                                website = website_guess
                        break

            # If we found useful information, return it
            if official_name or datasheet_url:
                return (official_name or manufacturer, website, datasheet_url)

            return None

        except Exception as e:
            return None

    def _extract_part_info_from_mouser_page(self, soup, manufacturer, part_number):
        """Extract manufacturer name, website, and datasheet link from Mouser page"""
        try:
            official_name = None
            website = None
            datasheet_url = None

            # Look for datasheet links
            for link in soup.find_all('a', href=True):
                href = link['href']
                link_text = link.get_text().strip().lower()

                # Check for datasheet links
                if any(keyword in link_text for keyword in ['datasheet', 'data sheet', 'pdf', 'specification']):
                    if href.startswith('http'):
                        datasheet_url = href
                    elif href.startswith('/'):
                        datasheet_url = urljoin('https://www.mouser.com', href)

                # Check for manufacturer website links
                elif href.startswith('http') and 'mouser.com' not in href:
                    if any(word in href.lower() or word in link_text
                          for word in manufacturer.lower().split() if len(word) > 2):
                        website = href
                        official_name = link_text.title() or manufacturer

            # Look for manufacturer information in page content
            page_text = soup.get_text()

            # Mouser-specific patterns
            mfg_patterns = [
                r'Manufacturer[:\s]+([^,\n\|]+)',
                r'Brand[:\s]+([^,\n\|]+)',
                r'Mfr[:\s]+([^,\n\|]+)',
            ]

            for pattern in mfg_patterns:
                match = re.search(pattern, page_text, re.I)
                if match:
                    found_mfg = match.group(1).strip()
                    found_mfg = re.sub(r'\s+', ' ', found_mfg)
                    found_mfg = found_mfg.split('|')[0].strip()

                    if len(found_mfg) > 2 and not official_name:
                        official_name = found_mfg

                        if not website:
                            website_guess = f"https://www.{found_mfg.lower().replace(' ', '').replace('-', '')}.com"
                            if self._test_manufacturer_website(website_guess, manufacturer):
                                website = website_guess
                        break

            if official_name or datasheet_url:
                return (official_name or manufacturer, website, datasheet_url)

            return None

        except Exception as e:
            return None

    def _extract_part_info_from_mouser_result(self, result_element, manufacturer, part_number):
        """Extract manufacturer name, website, and datasheet link from Mouser search result"""
        try:
            # Similar to Mouser page extraction but for search results
            return self._extract_part_info_from_mouser_page(result_element, manufacturer, part_number)
        except Exception as e:
            return None

    def download_file_from_url(self, url, manufacturer, part_number, file_type):
        """Download a file directly from a URL"""
        try:
            self.add_comment(f"📥 Downloading {file_type} from: {url[:50]}...")

            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                # Determine file extension
                content_type = response.headers.get('content-type', '').lower()
                if 'pdf' in content_type or url.lower().endswith('.pdf'):
                    extension = '.pdf'
                elif 'step' in content_type or url.lower().endswith(('.step', '.stp')):
                    extension = '.step'
                else:
                    extension = '.pdf'  # Default to PDF for datasheets

                # Create filename
                safe_manufacturer = manufacturer.replace(' ', '_').replace('/', '_')
                safe_part = part_number.replace('/', '_').replace('\\', '_')
                filename = f"{safe_manufacturer} {safe_part}_{file_type}{extension}"

                # Determine directory
                if file_type == 'datasheet' or extension == '.pdf':
                    file_path = self.datasheet_dir / filename
                else:
                    file_path = self.model_3d_dir / filename

                # Save file
                with open(file_path, 'wb') as f:
                    f.write(response.content)

                self.add_comment(f"✅ Downloaded: {filename}")

                # Update status fields
                if file_type == 'datasheet':
                    self.pdf_file_text.set(filename)
                else:
                    self.step_file_text.set(filename)

                return True
            else:
                self.add_comment(f"❌ Download failed: HTTP {response.status_code}")
                return False

        except Exception as e:
            self.add_comment(f"❌ Download error: {str(e)[:50]}")
            return False

    def find_manufacturer_website(self, manufacturer):
        """Try to automatically find manufacturer website"""
        self.add_comment(f"🔍 Searching for {manufacturer} website...")

        manufacturer_lower = manufacturer.lower().strip()

        # Step 1: Check CSV file first
        if manufacturer_lower in self.manufacturer_websites:
            website = self.manufacturer_websites[manufacturer_lower]
            self.add_comment(f"✅ Found in CSV: {website}")
            return website

        # Step 2: Check for known manufacturer variations
        known_variations = {
            'wurth': 'https://www.we-online.com',
            'würth': 'https://www.we-online.com',
            'wurth elektronik': 'https://www.we-online.com',
            'würth elektronik': 'https://www.we-online.com',
            'we-online': 'https://www.we-online.com',
            'vishay': 'https://www.vishay.com',
            'bourns': 'https://www.bourns.com',
            'murata': 'https://www.murata.com',
            'tdk': 'https://www.tdk-electronics.tdk.com'
        }

        if manufacturer_lower in known_variations:
            website = known_variations[manufacturer_lower]
            self.add_comment(f"✅ Found known manufacturer: {manufacturer} → {website}")
            # Save to CSV for future use
            self._save_manufacturer_to_csv(manufacturer, website)
            return website

        # Step 3: Check for partial matches in CSV (e.g., "TI" for "Texas Instruments")
        for csv_manufacturer, website in self.manufacturer_websites.items():
            if (manufacturer_lower in csv_manufacturer or
                csv_manufacturer in manufacturer_lower or
                any(word in csv_manufacturer.split() for word in manufacturer_lower.split() if len(word) > 2)):
                self.add_comment(f"✅ Found partial match in CSV: {csv_manufacturer} -> {website}")
                return website
        
        # Step 3: Search Digi-Key to find correct manufacturer info
        digikey_result = self._search_digikey_for_manufacturer(manufacturer)
        if digikey_result:
            return digikey_result

        # Step 4: Search the web for the manufacturer (fallback)
        web_result = self._search_web_for_manufacturer(manufacturer)
        if web_result:
            return web_result

        # Step 4: Try common patterns
        common_patterns = [
            f"https://www.{manufacturer_lower.replace(' ', '')}.com",
            f"https://www.{manufacturer_lower.replace(' ', '-')}.com",
            f"https://{manufacturer_lower.replace(' ', '')}.com"
        ]

        for pattern in common_patterns:
            try:
                self.add_comment(f"   Trying: {pattern}")
                response = self.session.get(pattern, timeout=10)
                if response.status_code == 200:
                    self.add_comment(f"✅ Found website: {pattern}")
                    return pattern
            except:
                continue

        self.add_comment(f"❌ Could not automatically find website for {manufacturer}")
        return None

    def _search_digikey_for_manufacturer(self, manufacturer):
        """Search Digi-Key to find correct manufacturer name and website"""
        try:
            self.add_comment(f"🔍 Searching Digi-Key for {manufacturer} information...")

            # Search Digi-Key for the manufacturer
            search_query = f"{manufacturer} manufacturer"
            digikey_search_url = f"https://www.digikey.com/en/products/filter/manufacturers/{quote(search_query)}"

            # Also try direct manufacturer search
            digikey_mfg_url = f"https://www.digikey.com/en/supplier-centers/{quote(manufacturer.lower().replace(' ', '-'))}"

            # Try manufacturer listing page
            digikey_list_url = "https://www.digikey.com/en/suppliers"

            urls_to_try = [digikey_mfg_url, digikey_search_url, digikey_list_url]

            for url in urls_to_try:
                try:
                    self.add_comment(f"   Checking: {url[:50]}...")

                    response = self.session.get(url, timeout=15)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Look for manufacturer information on Digi-Key
                        manufacturer_info = self._extract_manufacturer_from_digikey(soup, manufacturer)
                        if manufacturer_info:
                            official_name, website = manufacturer_info
                            self.add_comment(f"✅ Found on Digi-Key: {official_name} → {website}")

                            # Save to CSV with the official name
                            self._save_manufacturer_to_csv(official_name, website)

                            # Also save with the user's input name for future searches
                            if official_name.lower() != manufacturer.lower():
                                self._save_manufacturer_to_csv(manufacturer, website)

                            return website

                except Exception as e:
                    self.add_comment(f"   ❌ Failed to check {url[:30]}: {str(e)[:30]}")
                    continue

            # Try searching for parts from this manufacturer on Digi-Key
            return self._search_digikey_parts_by_manufacturer(manufacturer)

        except Exception as e:
            self.add_comment(f"⚠️ Digi-Key search failed: {str(e)[:50]}")
            return None

    def _extract_manufacturer_from_digikey(self, soup, manufacturer):
        """Extract manufacturer name and website from Digi-Key page"""
        try:
            manufacturer_lower = manufacturer.lower()

            # Look for manufacturer links and information
            # Digi-Key often has manufacturer pages with official websites

            # Method 1: Look for supplier/manufacturer links
            for link in soup.find_all('a', href=True):
                link_text = link.get_text().strip().lower()
                href = link['href']

                # Check if this link mentions our manufacturer
                if manufacturer_lower in link_text or any(word in link_text for word in manufacturer_lower.split() if len(word) > 2):

                    # Look for external website links (not Digi-Key internal)
                    if href.startswith('http') and 'digikey.com' not in href:
                        # This might be the manufacturer's website
                        return (link.get_text().strip(), href)

                    # Look for Digi-Key manufacturer pages that might contain website info
                    elif 'supplier' in href or 'manufacturer' in href:
                        # Follow this link to get more manufacturer info
                        try:
                            full_url = urljoin('https://www.digikey.com', href)
                            mfg_response = self.session.get(full_url, timeout=10)
                            if mfg_response.status_code == 200:
                                mfg_soup = BeautifulSoup(mfg_response.text, 'html.parser')

                                # Look for external website links on the manufacturer page
                                for ext_link in mfg_soup.find_all('a', href=True):
                                    ext_href = ext_link['href']
                                    if ext_href.startswith('http') and 'digikey.com' not in ext_href:
                                        # Check if this looks like a manufacturer website
                                        if any(word in ext_href.lower() for word in manufacturer_lower.split() if len(word) > 2):
                                            return (manufacturer, ext_href)
                        except:
                            continue

            # Method 2: Look in page content for manufacturer website mentions
            page_text = soup.get_text().lower()

            # Look for website patterns in the text
            import re
            website_patterns = [
                rf'{re.escape(manufacturer_lower.replace(" ", ""))}\.(com|net|org)',
                rf'www\.{re.escape(manufacturer_lower.replace(" ", ""))}\.(com|net|org)',
                rf'{re.escape(manufacturer_lower.replace(" ", "-"))}\.(com|net|org)'
            ]

            for pattern in website_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    domain = f"https://www.{manufacturer_lower.replace(' ', '')}.{matches[0]}"
                    return (manufacturer, domain)

            return None

        except Exception as e:
            return None

    def _search_digikey_parts_by_manufacturer(self, manufacturer):
        """Search for parts by manufacturer on Digi-Key to find manufacturer info"""
        try:
            self.add_comment(f"🔍 Searching Digi-Key parts for {manufacturer}...")

            # Search for parts from this manufacturer
            search_url = f"https://www.digikey.com/en/products/filter?keywords={quote(manufacturer)}&ColumnSort=0&page=1&pageSize=25"

            response = self.session.get(search_url, timeout=15)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for manufacturer information in search results
                # Digi-Key search results often show manufacturer names and links

                for result in soup.find_all(['div', 'td', 'span'], class_=re.compile(r'manufacturer|mfg|supplier', re.I)):
                    result_text = result.get_text().strip()

                    # Check if this mentions our manufacturer
                    if manufacturer.lower() in result_text.lower():

                        # Look for links in this result
                        for link in result.find_all('a', href=True):
                            href = link['href']

                            # External website link
                            if href.startswith('http') and 'digikey.com' not in href:
                                self.add_comment(f"   Found potential website: {href}")

                                # Test if this is a valid manufacturer website
                                if self._test_manufacturer_website(href, manufacturer):
                                    return href

                # Alternative: Look for any external links that might be manufacturer websites
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    link_text = link.get_text().lower()

                    if (href.startswith('http') and 'digikey.com' not in href and
                        any(word in href.lower() or word in link_text for word in manufacturer.lower().split() if len(word) > 2)):

                        if self._test_manufacturer_website(href, manufacturer):
                            return href

            return None

        except Exception as e:
            self.add_comment(f"⚠️ Digi-Key parts search failed: {str(e)[:50]}")
            return None

    def _search_web_for_manufacturer(self, manufacturer):
        """Search the web to find manufacturer's official website using Google"""
        try:
            self.add_comment(f"🌐 Searching Google for {manufacturer} official website...")

            # Try Google search first (more reliable for electronics manufacturers)
            google_result = self._google_search_manufacturer(manufacturer)
            if google_result:
                return google_result

            # Fallback to DuckDuckGo if Google fails
            return self._duckduckgo_search_manufacturer(manufacturer)

        except Exception as e:
            self.add_comment(f"⚠️ Web search failed: {str(e)[:100]}")
            return None

    def _google_search_manufacturer(self, manufacturer):
        """Primary Google search for electronics manufacturers"""
        try:
            # Multiple search strategies for better results
            search_queries = [
                f"{manufacturer} electronics components official website",
                f"{manufacturer} semiconductor datasheet site:com",
                f"{manufacturer} electronic parts catalog",
                f'"{manufacturer}" electronics manufacturer website'
            ]

            for query in search_queries:
                self.add_comment(f"🔍 Google search: {query[:50]}...")

                google_url = f"https://www.google.com/search?q={quote(query)}&num=10"

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }

                response = requests.get(google_url, headers=headers, timeout=15)
                if response.status_code != 200:
                    continue

                soup = BeautifulSoup(response.text, 'html.parser')
                potential_websites = []

                # Look for search result links in various Google result formats
                # Standard search results
                for result in soup.find_all('div', class_='g'):
                    link_elem = result.find('a', href=True)
                    if link_elem and link_elem['href'].startswith('http'):
                        url = link_elem['href']
                        title = result.get_text().lower()

                        # Check if this looks like a manufacturer website
                        if self._is_likely_manufacturer_site(url, title, manufacturer):
                            potential_websites.append(url)

                # Alternative: Look for any links that might be search results
                if not potential_websites:
                    for link in soup.find_all('a', href=True):
                        href = link['href']

                        # Skip Google internal links
                        if any(skip in href for skip in ['google.com', 'youtube.com', 'maps.google', 'translate.google']):
                            continue

                        # Extract actual URL from Google redirect
                        if '/url?q=' in href:
                            try:
                                actual_url = href.split('/url?q=')[1].split('&')[0]
                                from urllib.parse import unquote
                                actual_url = unquote(actual_url)

                                if actual_url.startswith('http') and self._is_likely_manufacturer_site(actual_url, link.get_text(), manufacturer):
                                    potential_websites.append(actual_url)
                            except:
                                continue
                        elif href.startswith('http') and self._is_likely_manufacturer_site(href, link.get_text(), manufacturer):
                            potential_websites.append(href)

                # Test the most promising websites
                for website in potential_websites[:3]:  # Test top 3 per query
                    result = self._test_manufacturer_website(website, manufacturer)
                    if result:
                        return result

                # Small delay between queries to be respectful
                import time
                time.sleep(1)

            return None

        except Exception as e:
            self.add_comment(f"⚠️ Google search failed: {str(e)[:50]}")
            return None

    def _is_likely_manufacturer_site(self, url, text, manufacturer):
        """Check if URL/text suggests this is a manufacturer website"""
        url_lower = url.lower()
        text_lower = text.lower()
        manufacturer_words = [word for word in manufacturer.lower().split() if len(word) > 2]

        # Skip obviously irrelevant sites
        skip_domains = ['wikipedia', 'amazon', 'ebay', 'alibaba', 'digikey', 'mouser', 'octopart', 'findchips']
        if any(skip in url_lower for skip in skip_domains):
            return False

        # Check if manufacturer name appears in URL or text
        manufacturer_in_url = any(word in url_lower for word in manufacturer_words)
        manufacturer_in_text = any(word in text_lower for word in manufacturer_words)

        # Look for electronics-related terms
        electronics_terms = ['electronic', 'semiconductor', 'component', 'datasheet', 'manufacturer']
        electronics_match = any(term in text_lower for term in electronics_terms)

        return (manufacturer_in_url or manufacturer_in_text) and (electronics_match or manufacturer_in_url)

    def _test_manufacturer_website(self, website, manufacturer):
        """Test if a website is a valid manufacturer website"""
        try:
            # Clean up URL
            clean_url = website.split('?')[0].split('#')[0]

            self.add_comment(f"   Testing: {clean_url}")

            test_response = self.session.get(clean_url, timeout=12)
            if test_response.status_code == 200:
                page_content = test_response.text.lower()

                # Enhanced validation for electronics manufacturers
                electronics_keywords = [
                    'electronic', 'component', 'semiconductor', 'datasheet',
                    'product', 'catalog', 'parts', 'ic', 'chip', 'circuit',
                    'resistor', 'capacitor', 'inductor', 'diode', 'transistor',
                    'microcontroller', 'sensor', 'connector', 'pcb'
                ]

                keyword_count = sum(1 for keyword in electronics_keywords if keyword in page_content)

                # Check if manufacturer name appears on the page
                manufacturer_words = [word for word in manufacturer.lower().split() if len(word) > 2]
                manufacturer_on_page = any(word in page_content for word in manufacturer_words)

                # More lenient scoring for manufacturer websites
                if keyword_count >= 1 and manufacturer_on_page:
                    self.add_comment(f"✅ Found manufacturer website via Google: {clean_url}")
                    # Save to CSV for future use
                    self._save_manufacturer_to_csv(manufacturer, clean_url)
                    return clean_url
                elif manufacturer_on_page and any(term in page_content for term in ['about', 'company', 'contact']):
                    # Even if not many electronics terms, if manufacturer name is there and it's a company site
                    self.add_comment(f"✅ Found manufacturer website (company site): {clean_url}")
                    self._save_manufacturer_to_csv(manufacturer, clean_url)
                    return clean_url
                else:
                    self.add_comment(f"   ❌ Not a manufacturer site (keywords: {keyword_count}, manufacturer: {manufacturer_on_page})")

        except Exception as e:
            self.add_comment(f"   ❌ Failed to test {clean_url}: {str(e)[:50]}")

        return None

    def _duckduckgo_search_manufacturer(self, manufacturer):
        """Fallback DuckDuckGo search method"""
        try:
            self.add_comment(f"🔍 Trying DuckDuckGo search for {manufacturer}...")

            # Use DuckDuckGo search as fallback
            search_query = f"{manufacturer} official website electronics components"
            search_url = f"https://duckduckgo.com/html/?q={quote(search_query)}"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(search_url, headers=headers, timeout=15)
            if response.status_code != 200:
                self.add_comment("⚠️ DuckDuckGo search service unavailable")
                return None

            soup = BeautifulSoup(response.text, 'html.parser')
            potential_websites = []

            # Find links in search results
            for link in soup.find_all('a', href=True):
                href = link['href']

                # Skip DuckDuckGo internal links
                if 'duckduckgo.com' in href or href.startswith('/'):
                    continue

                # Look for manufacturer name in the URL or link text
                link_text = link.get_text().lower()

                if href.startswith('http') and self._is_likely_manufacturer_site(href, link_text, manufacturer):
                    potential_websites.append(href)

            # Test the most promising websites
            for website in potential_websites[:3]:  # Test top 3 results
                result = self._test_manufacturer_website(website, manufacturer)
                if result:
                    return result

            return None

        except Exception as e:
            self.add_comment(f"⚠️ DuckDuckGo search failed: {str(e)[:50]}")
            return None

    def analyze_datasheet_url_for_step_clues(self, datasheet_url, part_number):
        """Analyze datasheet URL to find clues for locating STEP files"""
        clues = []

        try:
            self.add_comment(f"🔍 Analyzing datasheet URL for STEP clues...")

            # Extract useful information from the datasheet URL
            url_lower = datasheet_url.lower()

            # Common patterns in manufacturer URLs
            patterns_to_extract = [
                # Product family/series patterns
                r'/([a-z0-9]+[-_]?series?[-_]?[a-z0-9]*)',
                r'/([a-z0-9]*[-_]?family[-_]?[a-z0-9]*)',

                # Package/footprint patterns
                r'/(sot[-_]?\d+)',
                r'/(qfn[-_]?\d+)',
                r'/(bga[-_]?\d+)',
                r'/(tssop[-_]?\d+)',
                r'/(soic[-_]?\d+)',
                r'/(dip[-_]?\d+)',
                r'/(smd[-_]?\d+)',

                # Connector specific patterns (for WURTH)
                r'/(connector[s]?)',
                r'/(header[s]?)',
                r'/(socket[s]?)',
                r'/(terminal[s]?)',
                r'/(plug[s]?)',

                # Directory structure clues
                r'/([a-z0-9]+)/datasheets?/',
                r'/([a-z0-9]+)/documents?/',
                r'/([a-z0-9]+)/media/',
                r'/([a-z0-9]+)/files?/',

                # Part number variations
                rf'/({re.escape(part_number[:6])}[a-z0-9]*)',  # First 6 chars + variations
                rf'/({re.escape(part_number[:4])}[a-z0-9]*)',  # First 4 chars + variations
            ]

            for pattern in patterns_to_extract:
                matches = re.findall(pattern, url_lower)
                for match in matches:
                    if len(match) > 2 and match not in clues:
                        clues.append(match)

            # Extract base URL structure
            if 'we-online.com' in url_lower:
                # Würth Elektronik specific patterns
                if '/catalog/' in url_lower:
                    clues.append('catalog_structure')
                if '/media/' in url_lower:
                    clues.append('media_directory')
                if '/datasheet/' in url_lower:
                    clues.append('datasheet_directory')

            # Look for version numbers or revision codes
            version_patterns = [
                r'[_-]v(\d+)',
                r'[_-]rev(\d+)',
                r'[_-]r(\d+)',
                r'[_-](\d+\.\d+)'
            ]

            for pattern in version_patterns:
                matches = re.findall(pattern, url_lower)
                for match in matches:
                    clues.append(f'version_{match}')

            return clues

        except Exception as e:
            self.add_comment(f"⚠️ Error analyzing datasheet URL: {str(e)[:50]}")
            return []

    def search_for_step_files_with_clues(self, website, part_number, clues):
        """Search for STEP files using clues from datasheet URL analysis"""
        try:
            self.add_comment(f"🔍 Searching for STEP files using URL clues...")

            # Generate potential STEP file URLs based on clues
            step_urls = []

            # Base patterns for STEP files
            base_patterns = [
                f"{website}/media/3d/{part_number}.step",
                f"{website}/media/3d/{part_number}.stp",
                f"{website}/catalog/3d/{part_number}.step",
                f"{website}/catalog/3d/{part_number}.stp",
                f"{website}/files/3d/{part_number}.step",
                f"{website}/files/3d/{part_number}.stp",
                f"{website}/downloads/3d/{part_number}.step",
                f"{website}/downloads/3d/{part_number}.stp",
            ]

            # Add clue-based patterns
            for clue in clues:
                if clue == 'catalog_structure':
                    step_urls.extend([
                        f"{website}/catalog/3d/{part_number}.step",
                        f"{website}/catalog/3d/{part_number}.stp",
                        f"{website}/catalog/media/3d/{part_number}.step",
                        f"{website}/catalog/media/3d/{part_number}.stp",
                    ])
                elif clue == 'media_directory':
                    step_urls.extend([
                        f"{website}/media/3d/{part_number}.step",
                        f"{website}/media/3d/{part_number}.stp",
                        f"{website}/media/cad/{part_number}.step",
                        f"{website}/media/cad/{part_number}.stp",
                    ])
                elif 'connector' in clue:
                    step_urls.extend([
                        f"{website}/media/3d/connectors/{part_number}.step",
                        f"{website}/media/3d/connectors/{part_number}.stp",
                        f"{website}/catalog/connectors/3d/{part_number}.step",
                        f"{website}/catalog/connectors/3d/{part_number}.stp",
                    ])
                elif clue.startswith('version_'):
                    version = clue.replace('version_', '')
                    step_urls.extend([
                        f"{website}/media/3d/{part_number}_v{version}.step",
                        f"{website}/media/3d/{part_number}_v{version}.stp",
                        f"{website}/catalog/3d/{part_number}_v{version}.step",
                        f"{website}/catalog/3d/{part_number}_v{version}.stp",
                    ])
                else:
                    # Use clue as potential directory or filename component
                    step_urls.extend([
                        f"{website}/media/3d/{clue}/{part_number}.step",
                        f"{website}/media/3d/{clue}/{part_number}.stp",
                        f"{website}/catalog/3d/{clue}/{part_number}.step",
                        f"{website}/catalog/3d/{clue}/{part_number}.stp",
                        f"{website}/media/3d/{part_number}_{clue}.step",
                        f"{website}/media/3d/{part_number}_{clue}.stp",
                    ])

            # Add base patterns to the list
            step_urls.extend(base_patterns)

            # Remove duplicates while preserving order
            unique_step_urls = []
            for url in step_urls:
                if url not in unique_step_urls:
                    unique_step_urls.append(url)

            # Test each potential STEP file URL
            for step_url in unique_step_urls[:15]:  # Test top 15 candidates
                try:
                    self.add_comment(f"   Testing: {step_url}")

                    response = self.session.head(step_url, timeout=10)  # Use HEAD to check existence
                    if response.status_code == 200:
                        # Found a STEP file! Download it
                        self.add_comment(f"✅ Found STEP file: {step_url}")

                        # Download the STEP file
                        download_response = self.session.get(step_url, timeout=30)
                        if download_response.status_code == 200:
                            # Determine filename
                            filename = f"{part_number.replace('/', '_')}_3d.step"
                            file_path = self.model_3d_dir / filename

                            # Save file
                            with open(file_path, 'wb') as f:
                                f.write(download_response.content)

                            self.add_comment(f"✅ Downloaded STEP file: {filename}")
                            self.step_file_text.set(filename)
                            return True

                    # Small delay between requests
                    import time
                    time.sleep(0.5)

                except Exception as e:
                    continue

            self.add_comment(f"❌ No STEP files found using URL clues")
            return False

        except Exception as e:
            self.add_comment(f"⚠️ Error searching for STEP files: {str(e)[:50]}")
            return False

    def ask_for_website(self, manufacturer):
        """Ask user to provide the manufacturer website"""
        website = tk.simpledialog.askstring(
            "Website Required",
            f"Could not find website for '{manufacturer}'.\n\nPlease enter the website URL (e.g., analog.com):",
            initialvalue=""
        )
        
        if website:
            # Clean up the URL
            if not website.startswith(('http://', 'https://')):
                website = f"https://www.{website}"
            
            self.add_comment(f"📝 User provided website: {website}")
            
            # Test the website
            try:
                response = self.session.get(website, timeout=15)
                if response.status_code == 200:
                    self.add_comment(f"✅ Website is accessible")

                    # Save the working website to CSV
                    self._save_manufacturer_to_csv(manufacturer, website)

                    # Continue with the search
                    part_number = self.part_number_var.get().strip()
                    threading.Thread(target=self.search_with_known_website,
                                   args=(manufacturer, part_number, website)).start()
                else:
                    self.add_comment(f"❌ Website returned error: {response.status_code}")
                    self.search_complete()
            except Exception as e:
                self.add_comment(f"❌ Cannot access website: {e}")
                self.search_complete()
        else:
            self.add_comment("❌ Search cancelled - no website provided")
            self.search_complete()
    
    def search_with_known_website(self, manufacturer, part_number, website_url):
        """Search manufacturer website using their actual search form"""
        try:
            self.add_comment(f"🔍 Searching {website_url} for part {part_number}")

            # Step 1: Get the homepage to find their search form
            self.add_comment(f"   Getting homepage to find search form...")
            homepage_response = self.session.get(website_url, timeout=20)

            if homepage_response.status_code != 200:
                self.add_comment(f"   ❌ Cannot access {website_url}")
                return

            # Step 2: Find and submit their search form
            soup = BeautifulSoup(homepage_response.text, 'html.parser')
            search_form = self.find_search_form(soup, website_url)

            if search_form:
                self.add_comment(f"   ✅ Found search form on website")
                success = self.submit_search_form(search_form, part_number, website_url)
                if success:
                    return

            # Step 3: If no form found, try common search URL patterns
            self.add_comment(f"   No search form found, trying common search URLs...")
            self.try_common_search_urls(website_url, part_number)

        except Exception as e:
            self.add_comment(f"❌ Search error: {e}")
            logger.error(f"Search error: {e}")

    def search_any_website(self, website_url, part_number):
        """Generic search that works with any website by finding and using their search form"""
        try:
            self.add_comment(f"🔍 Searching website {website_url} for {part_number}...")

            # Step 1: Get the homepage to find the search form
            self.add_comment(f"   Getting homepage to find search form...")
            homepage_response = self.session.get(website_url, timeout=20)

            if homepage_response.status_code != 200:
                self.add_comment(f"   ❌ Cannot access {website_url}")
                return False

            # Step 2: Find search form on the page
            soup = BeautifulSoup(homepage_response.text, 'html.parser')
            search_form = self.find_search_form(soup, website_url)

            if search_form:
                self.add_comment(f"   ✅ Found search form")
                return self.submit_search_form(search_form, part_number, website_url)
            else:
                self.add_comment(f"   ❌ No search form found, trying common search URLs...")
                return self.try_common_search_urls(website_url, part_number)

        except Exception as e:
            self.add_comment(f"⚠️ Website search failed: {str(e)[:50]}")
            return False

    def find_search_form(self, soup, website_url):
        """Find search form on any website"""
        # Look for forms with search-related attributes
        search_indicators = ['search', 'query', 'q', 'keyword', 'term', 'find']

        for form in soup.find_all('form'):
            # Check form action
            action = form.get('action', '').lower()
            if any(indicator in action for indicator in search_indicators):
                return form

            # Check input names
            for input_tag in form.find_all('input'):
                name = input_tag.get('name', '').lower()
                placeholder = input_tag.get('placeholder', '').lower()

                if any(indicator in name for indicator in search_indicators):
                    return form
                if any(indicator in placeholder for indicator in search_indicators):
                    return form

        return None

    def submit_search_form(self, form, part_number, website_url):
        """Submit the found search form with the part number"""
        try:
            # Get form action and method
            action = form.get('action', '')
            method = form.get('method', 'get').lower()

            # Build full URL for form action
            if action.startswith('http'):
                form_url = action
            elif action.startswith('/'):
                form_url = urljoin(website_url, action)
            else:
                form_url = urljoin(website_url, '/' + action)

            # Find the search input field
            search_input = None
            search_indicators = ['search', 'query', 'q', 'keyword', 'term', 'find']

            for input_tag in form.find_all('input'):
                name = input_tag.get('name', '').lower()
                if any(indicator in name for indicator in search_indicators):
                    search_input = input_tag.get('name')
                    break

            if not search_input:
                return False

            # Prepare form data
            form_data = {search_input: part_number}

            # Add other form fields if needed
            for input_tag in form.find_all('input'):
                if input_tag.get('type') == 'hidden':
                    name = input_tag.get('name')
                    value = input_tag.get('value', '')
                    if name:
                        form_data[name] = value

            self.add_comment(f"   Submitting search form with: {search_input}={part_number}")

            # Submit form
            if method == 'post':
                response = self.session.post(form_url, data=form_data, timeout=30)
            else:
                response = self.session.get(form_url, params=form_data, timeout=30)

            if response.status_code == 200:
                return self.parse_search_results(response.text, part_number, website_url)

            return False

        except Exception as e:
            self.add_comment(f"   ❌ Form submission failed: {str(e)[:30]}")
            return False

    def try_common_search_urls(self, website_url, part_number):
        """Try common search URL patterns when no form is found"""
        common_patterns = [
            f"{website_url}/search?q={part_number}",
            f"{website_url}/search?query={part_number}",
            f"{website_url}/search?keyword={part_number}",
            f"{website_url}/search?term={part_number}",
            f"{website_url}/search/{part_number}",
            f"{website_url}/products/search?q={part_number}",
            f"{website_url}/catalog/search?q={part_number}",
        ]

        for search_url in common_patterns:
            try:
                self.add_comment(f"   Trying: {search_url}")
                response = self.session.get(search_url, timeout=20)

                if response.status_code == 200:
                    if self.parse_search_results(response.text, part_number, website_url):
                        return True

            except Exception:
                continue

        return False

    def parse_search_results(self, html_content, part_number, website_url):
        """Parse search results - find part link and go to part detail page"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            page_text = soup.get_text().lower()

            # Check if part number is found
            if part_number.lower() in page_text:
                self.add_comment(f"✅ Found {part_number} on search results page")

                # Look for the part link to go to the part detail page
                part_detail_url = None

                for link in soup.find_all('a', href=True):
                    href = link['href']
                    link_text = link.get_text().strip()

                    # Look for links that contain the part number or lead to part details
                    if (part_number in link_text or
                        part_number.lower() in href.lower() or
                        any(keyword in link_text.lower() for keyword in ['detail', 'product', 'part', 'view'])):

                        # Make URL absolute
                        if href.startswith('http'):
                            part_detail_url = href
                        elif href.startswith('/'):
                            part_detail_url = urljoin(website_url, href)
                        else:
                            part_detail_url = urljoin(website_url, '/' + href)

                        self.add_comment(f"🔗 Found part detail link: {part_detail_url[:60]}...")
                        break

                if part_detail_url:
                    # Go to the part detail page and extract files (same method as Digi-Key)
                    return self.extract_files_from_part_page(part_detail_url, part_number)
                else:
                    self.add_comment(f"❌ No part detail link found")
                    return False

            else:
                self.add_comment(f"❌ Part {part_number} not found on search results page")
                return False

        except Exception as e:
            self.add_comment(f"   ❌ Parse error: {str(e)[:50]}")
            return False

    def extract_files_from_part_page(self, part_url, part_number):
        """Extract datasheet and STEP files from part detail page (same method as Digi-Key)"""
        try:
            self.add_comment(f"📄 Getting part detail page...")
            response = self.session.get(part_url, timeout=30)

            if response.status_code != 200:
                self.add_comment(f"   ❌ Cannot access part page")
                return False

            soup = BeautifulSoup(response.text, 'html.parser')
            self.add_comment(f"✅ Got part detail page")

            # Extract files using same method as Digi-Key
            datasheet_url = None
            step_file_url = None
            found_files = []

            for link in soup.find_all('a', href=True):
                href = link['href']
                link_text = link.get_text().lower()

                # Make URL absolute
                if href.startswith('http'):
                    full_url = href
                elif href.startswith('/'):
                    full_url = urljoin(part_url, href)
                else:
                    continue

                # Look for datasheet links (same as Digi-Key)
                if any(keyword in link_text for keyword in ['datasheet', 'pdf', 'data sheet']):
                    datasheet_url = full_url
                    self.add_comment(f"📄 Found datasheet: {datasheet_url[:60]}...")
                    found_files.append(('datasheet', datasheet_url))

                # Look for STEP files (same as Digi-Key)
                if (any(keyword in link_text for keyword in ['step', '3d', 'cad', 'model', 'download']) or
                    any(ext in full_url.lower() for ext in ['.step', '.stp'])):
                    step_file_url = full_url
                    self.add_comment(f"🔧 Found STEP file: {step_file_url[:60]}...")
                    found_files.append(('step', step_file_url))

            # Also scan for direct file links in the HTML (same as Digi-Key)
            for element in soup.find_all(['a', 'link'], href=True):
                href = element.get('href', '')
                if any(ext in href.lower() for ext in ['.step', '.stp', '.pdf']):
                    if href.startswith('http'):
                        file_url = href
                    elif href.startswith('/'):
                        file_url = urljoin(part_url, href)
                    else:
                        continue

                    if '.pdf' in href.lower() and not datasheet_url:
                        datasheet_url = file_url
                        self.add_comment(f"📄 Found direct PDF: {datasheet_url[:60]}...")
                        found_files.append(('datasheet', datasheet_url))
                    elif any(ext in href.lower() for ext in ['.step', '.stp']) and not step_file_url:
                        step_file_url = file_url
                        self.add_comment(f"🔧 Found direct STEP: {step_file_url[:60]}...")
                        found_files.append(('step', step_file_url))

            # Download files (same as Digi-Key)
            for file_type, file_url in found_files:
                if file_type == 'datasheet':
                    self.download_file_from_url(file_url, "Manufacturer", part_number, "datasheet")
                elif file_type == 'step':
                    self.download_file_from_url(file_url, "Manufacturer", part_number, "3d_model")

            if found_files:
                self.add_comment(f"✅ Successfully found {len(found_files)} files on part detail page")
                return True
            else:
                self.add_comment(f"❌ No files found on part detail page")
                return False

        except Exception as e:
            self.add_comment(f"   ❌ Error accessing part page: {str(e)[:50]}")
            return False

    def search_wurth_simple(self, manufacturer, part_number):
        """SIMPLE: Search WURTH website and find STEP file on results page"""
        try:
            # First check if we already know this part
            if self.check_known_wurth_parts(part_number):
                return True

            self.add_comment(f"🔍 Searching WURTH website for {part_number}...")

            # WURTH website search URL (correct format)
            search_url = f"https://www.we-online.com/en/components/products?sq={part_number}"

            import time
            time.sleep(1)

            self.add_comment(f"   Opening: {search_url}")
            response = self.session.get(search_url, timeout=30, allow_redirects=True)

            if response.status_code == 200:
                # Check if we were redirected to a product page
                final_url = response.url
                if final_url != search_url:
                    self.add_comment(f"   Redirected to: {final_url[:60]}...")

                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()

                # Check if our part number appears on the page (or in the URL)
                if part_number.lower() in page_text or part_number.lower() in final_url.lower():
                    self.add_comment(f"✅ Found {part_number} on WURTH website")

                    # Look for STEP file on THIS page (same method as Digi-Key datasheet)
                    step_file_url = None

                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        link_text = link.get_text().lower()

                        # Make URL absolute
                        if href.startswith('http'):
                            full_url = href
                        elif href.startswith('/'):
                            full_url = urljoin('https://www.we-online.com', href)
                        else:
                            continue

                        # Look for STEP files (same method as Digi-Key finds datasheet)
                        if (any(keyword in link_text for keyword in ['step', '3d', 'cad', 'model', 'download']) or
                            any(ext in full_url.lower() for ext in ['.step', '.stp'])):
                            step_file_url = full_url
                            self.add_comment(f"🔧 Found STEP file: {step_file_url[:60]}...")
                            break

                    # Also scan for direct STEP file links in HTML
                    if not step_file_url:
                        for element in soup.find_all(['a', 'link'], href=True):
                            href = element.get('href', '')
                            if any(ext in href.lower() for ext in ['.step', '.stp']):
                                if href.startswith('http'):
                                    step_file_url = href
                                elif href.startswith('/'):
                                    step_file_url = urljoin('https://www.we-online.com', href)

                                if step_file_url:
                                    self.add_comment(f"🔧 Found direct STEP link: {step_file_url[:60]}...")
                                    break

                    # Download STEP file
                    if step_file_url:
                        self.add_comment(f"📥 Downloading STEP file from WURTH...")
                        self.download_file_from_url(step_file_url, "Würth Elektronik", part_number, "3d_model")

                        # Save this part info for next time
                        self.save_wurth_part_info(part_number, None, step_file_url)

                        return True
                    else:
                        self.add_comment(f"❌ No STEP file found on WURTH page")
                        return False

                else:
                    self.add_comment(f"❌ Part not found on WURTH website")
                    return False

            else:
                self.add_comment(f"❌ HTTP {response.status_code}")
                return False

        except Exception as e:
            self.add_comment(f"⚠️ WURTH search failed: {str(e)[:50]}")
            return False

    def check_known_wurth_parts(self, part_number):
        """Check if we already know where to find this WURTH part's files"""
        try:
            wurth_key = "wurth"

            if (wurth_key in self.knowledge_base and
                "known_parts" in self.knowledge_base[wurth_key] and
                part_number in self.knowledge_base[wurth_key]["known_parts"]):

                part_info = self.knowledge_base[wurth_key]["known_parts"][part_number]
                self.add_comment(f"✅ Found {part_number} in saved WURTH parts!")

                # Download known files directly
                files_downloaded = 0

                if "datasheet" in part_info:
                    datasheet_url = part_info["datasheet"]
                    self.add_comment(f"📄 Using saved datasheet: {datasheet_url[:60]}...")
                    self.download_file_from_url(datasheet_url, "Würth Elektronik", part_number, "datasheet")
                    files_downloaded += 1

                if "step_file" in part_info:
                    step_url = part_info["step_file"]
                    self.add_comment(f"🔧 Using saved STEP file: {step_url[:60]}...")
                    self.download_file_from_url(step_url, "Würth Elektronik", part_number, "3d_model")
                    files_downloaded += 1

                if files_downloaded > 0:
                    self.add_comment(f"✅ Downloaded {files_downloaded} files using saved URLs")
                    return True

            return False

        except Exception as e:
            self.add_comment(f"⚠️ Error checking known parts: {str(e)[:50]}")
            return False

    def save_wurth_part_info(self, part_number, datasheet_url, step_file_url):
        """Save WURTH part information for future quick access"""
        try:
            wurth_key = "wurth"

            if wurth_key not in self.knowledge_base:
                self.knowledge_base[wurth_key] = {"known_parts": {}}

            if "known_parts" not in self.knowledge_base[wurth_key]:
                self.knowledge_base[wurth_key]["known_parts"] = {}

            # Save the part info
            part_info = {}
            if datasheet_url:
                part_info["datasheet"] = datasheet_url
            if step_file_url:
                part_info["step_file"] = step_file_url

            self.knowledge_base[wurth_key]["known_parts"][part_number] = part_info

            # Save to file
            self._save_knowledge_base()

            self.add_comment(f"💾 Saved {part_number} info for future quick access")

        except Exception as e:
            self.add_comment(f"⚠️ Error saving part info: {str(e)[:50]}")

    def check_known_wurth_parts(self, part_number):
        """Check if we already know where to find this WURTH part's files"""
        try:
            wurth_key = "wurth"

            if (wurth_key in self.knowledge_base and
                "known_parts" in self.knowledge_base[wurth_key] and
                part_number in self.knowledge_base[wurth_key]["known_parts"]):

                part_info = self.knowledge_base[wurth_key]["known_parts"][part_number]
                self.add_comment(f"✅ Found {part_number} in saved WURTH parts!")

                # Download known files
                if "datasheet" in part_info:
                    datasheet_url = part_info["datasheet"]
                    self.add_comment(f"📄 Using saved datasheet: {datasheet_url[:60]}...")
                    self.download_file_from_url(datasheet_url, "Würth Elektronik", part_number, "datasheet")

                if "step_file" in part_info:
                    step_url = part_info["step_file"]
                    self.add_comment(f"🔧 Using saved STEP file: {step_url[:60]}...")
                    self.download_file_from_url(step_url, "Würth Elektronik", part_number, "3d_model")

                return True

            return False

        except Exception as e:
            self.add_comment(f"⚠️ Error checking known parts: {str(e)[:50]}")
            return False

    def search_with_learned_patterns(self, manufacturer, part_number):
        """Use previously learned patterns"""
        manufacturer_key = manufacturer.lower().strip()
        knowledge = self.knowledge_base[manufacturer_key]
        
        # Try learned search URLs
        for search_url_pattern in knowledge["search_patterns"]["successful_urls"]:
            try:
                # Replace part number in the pattern
                search_url = re.sub(r'q=[^&]+', f'q={quote(part_number)}', search_url_pattern)
                
                self.add_comment(f"📚 Trying learned pattern: {search_url}")
                
                response = self.session.get(search_url, timeout=30)
                if response.status_code == 200 and part_number.lower() in response.text.lower():
                    self.add_comment("✅ Learned pattern worked!")
                    
                    # Extract part page and continue
                    part_html, part_page_url = self.extract_part_page_from_search(response.text, part_number, knowledge["base_url"])
                    
                    if part_html and part_page_url:
                        self.process_part_page(part_html, part_page_url, manufacturer, part_number)
                        return True
                        
            except Exception as e:
                self.add_comment(f"❌ Learned pattern failed: {e}")
                continue
        
        return False
    
    def discover_search_patterns(self, manufacturer, part_number, website_url):
        """Discover search patterns for unknown manufacturer"""
        self.add_comment(f"🔍 Discovering search patterns for {manufacturer}...")
        
        # Try common search patterns
        domain = urlparse(website_url).netloc
        base_url = f"{urlparse(website_url).scheme}://{domain}"
        
        search_patterns = [
            f"{base_url}/search?q={quote(part_number)}",
            f"{base_url}/search/?q={quote(part_number)}",
            f"{base_url}/products/search?q={quote(part_number)}",
            f"{base_url}/part/search?q={quote(part_number)}",
            f"{base_url}/search?query={quote(part_number)}",
            f"{base_url}/search?part={quote(part_number)}"
        ]
        
        for search_url in search_patterns:
            try:
                self.add_comment(f"   Trying: {search_url}")
                response = self.session.get(search_url, timeout=20)
                
                if response.status_code == 200 and part_number.lower() in response.text.lower():
                    self.add_comment(f"✅ Found working search pattern!")
                    
                    # Learn from this success
                    self.learn_new_manufacturer(manufacturer, base_url, search_url, "GET")
                    
                    # Extract part page
                    part_html, part_page_url = self.extract_part_page_from_search(response.text, part_number, base_url)
                    
                    if part_html and part_page_url:
                        self.process_part_page(part_html, part_page_url, manufacturer, part_number)
                        return
                    
            except Exception as e:
                self.add_comment(f"   ❌ Failed: {e}")
                continue
        
        # If discovery fails, ask for help
        self.add_comment("❌ Could not discover search patterns automatically")
        self.add_comment("🤝 Need manual assistance to learn this manufacturer's website")
        self.root.after(0, self.ask_for_manual_help, manufacturer, part_number, website_url)
    
    def ask_for_manual_help(self, manufacturer, part_number, website_url):
        """Ask user for manual help when automatic discovery fails"""
        help_msg = f"""Could not automatically find search patterns for {manufacturer}.

Website: {website_url}
Part: {part_number}

Would you like to:
1. Manually navigate to find the part page URL
2. Skip this search
3. Try a different approach"""
        
        result = messagebox.askyesnocancel("Manual Help Needed", help_msg)
        
        if result is True:  # Yes - manual help
            self.manual_part_url_input(manufacturer, part_number, website_url)
        elif result is False:  # No - try different approach
            self.add_comment("🔄 Trying alternative search methods...")
            # Could implement alternative methods here
        else:  # Cancel
            self.add_comment("❌ Search cancelled by user")
        
        self.search_complete()

    def learn_new_manufacturer(self, manufacturer, base_url, search_url, method):
        """Learn patterns for a new manufacturer"""
        manufacturer_key = manufacturer.lower().strip()

        self.knowledge_base[manufacturer_key] = {
            "name": manufacturer,
            "base_url": base_url,
            "search_patterns": {
                "successful_urls": [search_url],
                "search_methods": [method],
                "part_extraction_patterns": []
            },
            "datasheet_patterns": {
                "url_patterns": [],
                "link_selectors": []
            },
            "3d_model_patterns": {
                "url_patterns": [],
                "link_selectors": [],
                "file_extensions": [".step", ".stp"]
            },
            "package_extraction": {
                "table_patterns": [],
                "text_patterns": []
            },
            "success_count": 1,
            "last_updated": time.time()
        }

        self._save_knowledge_base()
        self.add_comment(f"📚 Learned new manufacturer: {manufacturer}")

    def learn_from_successful_extraction(self, manufacturer, datasheet_url, model_urls, package_type):
        """Learn from successful datasheet and 3D model extraction"""
        manufacturer_key = manufacturer.lower().strip()

        if manufacturer_key not in self.knowledge_base:
            return

        knowledge = self.knowledge_base[manufacturer_key]
        base_url = knowledge["base_url"]

        # Learn datasheet pattern
        if datasheet_url and base_url in datasheet_url:
            pattern = datasheet_url.replace(base_url, "")
            if pattern not in knowledge["datasheet_patterns"]["url_patterns"]:
                knowledge["datasheet_patterns"]["url_patterns"].append(pattern)
                self.add_comment(f"📚 Learned datasheet pattern: {pattern}")

        # Learn 3D model patterns
        for url in model_urls:
            if base_url in url:
                pattern = url.replace(base_url, "")
                # Generalize pattern by replacing package name with placeholder
                if package_type:
                    generalized = pattern.replace(package_type, "{package}").replace(package_type.lower(), "{package}")
                    if generalized not in knowledge["3d_model_patterns"]["url_patterns"]:
                        knowledge["3d_model_patterns"]["url_patterns"].append(generalized)
                        self.add_comment(f"📚 Learned 3D model pattern: {generalized}")

        knowledge["success_count"] += 1
        knowledge["last_updated"] = time.time()
        self._save_knowledge_base()

    def download_file(self, url, directory, filename):
        """Download file to specific directory, replacing existing files"""
        try:
            self.add_comment(f"📥 Downloading: {os.path.basename(url)}")

            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()

            filepath = directory / filename

            # Check if file already exists and delete it
            if filepath.exists():
                try:
                    filepath.unlink()  # Delete the existing file
                    self.add_comment(f"🗑️ Deleted existing file: {filename}")
                except Exception as e:
                    self.add_comment(f"⚠️ Could not delete existing file: {e}")

            # Also check for and delete similar files (with counters)
            self._delete_similar_files(directory, filename)

            # Download the new file
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            file_size = os.path.getsize(filepath)
            self.add_comment(f"✅ Saved: {filename} ({file_size:,} bytes)")
            return str(filepath)

        except Exception as e:
            self.add_comment(f"❌ Download failed: {e}")
            return None

    def _delete_similar_files(self, directory, target_filename):
        """Delete files with similar names (e.g., with _1, _2 suffixes)"""
        try:
            target_path = Path(target_filename)
            base_name = target_path.stem
            extension = target_path.suffix

            # Look for files with the same base name and extension
            pattern_variations = [
                f"{base_name}_*{extension}",  # Files with _1, _2, etc.
                f"{base_name}*{extension}"    # Files with any suffix
            ]

            deleted_count = 0
            for pattern in pattern_variations:
                for existing_file in directory.glob(pattern):
                    if existing_file.name != target_filename:  # Don't delete the target file itself
                        try:
                            existing_file.unlink()
                            deleted_count += 1
                            self.add_comment(f"🗑️ Deleted old version: {existing_file.name}")
                        except Exception as e:
                            self.add_comment(f"⚠️ Could not delete {existing_file.name}: {e}")

            if deleted_count > 0:
                self.add_comment(f"🧹 Cleaned up {deleted_count} old file(s)")

        except Exception as e:
            self.add_comment(f"⚠️ Error during cleanup: {e}")

    def _cleanup_existing_files(self, manufacturer, part_number):
        """Clean up any existing files for this specific part"""
        try:
            manufacturer_clean = manufacturer.replace(" ", "_").replace(".", "").replace(",", "")

            # Patterns to look for
            datasheet_patterns = [
                f"{manufacturer_clean} {part_number}_datasheet*.pdf",
                f"{part_number}_datasheet*.pdf",
                f"{manufacturer_clean}*{part_number}*.pdf"
            ]

            model_patterns = [
                f"{manufacturer_clean} {part_number}_*.step",
                f"{manufacturer_clean} {part_number}_*.stp",
                f"{part_number}_*.step",
                f"{part_number}_*.stp"
            ]

            deleted_files = []

            # Clean up datasheets
            for pattern in datasheet_patterns:
                for file_path in self.datasheet_dir.glob(pattern):
                    try:
                        file_path.unlink()
                        deleted_files.append(f"📄 {file_path.name}")
                    except Exception as e:
                        self.add_comment(f"⚠️ Could not delete {file_path.name}: {e}")

            # Clean up 3D models
            for pattern in model_patterns:
                for file_path in self.model_3d_dir.glob(pattern):
                    try:
                        file_path.unlink()
                        deleted_files.append(f"🎯 {file_path.name}")
                    except Exception as e:
                        self.add_comment(f"⚠️ Could not delete {file_path.name}: {e}")

            if deleted_files:
                self.add_comment(f"🧹 Cleaned up {len(deleted_files)} existing file(s) for {part_number}:")
                for file_info in deleted_files:
                    self.add_comment(f"   🗑️ {file_info}")
            else:
                self.add_comment(f"✨ No existing files found for {part_number}")

        except Exception as e:
            self.add_comment(f"⚠️ Error during file cleanup: {e}")

    def select_3d_models_old(self, model_urls, part_number, package_type):
        """Show dialog to let user select which 3D models to download"""
        import tkinter as tk
        from tkinter import ttk

        selected_urls = []

        # Create selection dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Select 3D Models to Download")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # Main frame
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text=f"Found {len(model_urls)} 3D models for {part_number}",
                               font=('TkDefaultFont', 10, 'bold'))
        title_label.pack(pady=(0, 10))

        # Instructions
        instr_label = ttk.Label(main_frame, text="Select which models to download:")
        instr_label.pack(anchor=tk.W)

        # Scrollable frame for checkboxes
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Variables to track selections
        selection_vars = []

        # Create checkboxes for each model
        for i, url in enumerate(model_urls):
            var = tk.BooleanVar(value=True)  # Default to selected
            selection_vars.append(var)

            # Extract meaningful info from URL
            url_parts = url.split('/')
            filename = url_parts[-1] if url_parts else url

            # Try to identify package type from URL
            url_lower = url.lower()
            identified_package = None
            for pkg in ['sot23', 'sot25', 'sot323', 'sc59', 'sc70', 'soic', 'qfn']:
                if pkg in url_lower:
                    identified_package = pkg.upper()
                    break

            # Create descriptive text
            if identified_package:
                if identified_package == package_type:
                    description = f"✅ {filename} (Package: {identified_package} - MATCHES)"
                    checkbox_text = f"{i+1}. {filename} - {identified_package} ⭐ RECOMMENDED"
                else:
                    description = f"⚠️ {filename} (Package: {identified_package})"
                    checkbox_text = f"{i+1}. {filename} - {identified_package}"
                    var.set(False)  # Unselect non-matching packages by default
            else:
                description = f"❓ {filename}"
                checkbox_text = f"{i+1}. {filename}"

            # Checkbox frame
            cb_frame = ttk.Frame(scrollable_frame)
            cb_frame.pack(fill=tk.X, pady=2)

            # Checkbox
            checkbox = ttk.Checkbutton(cb_frame, text=checkbox_text, variable=var)
            checkbox.pack(anchor=tk.W)

            # URL label (smaller text)
            url_label = ttk.Label(cb_frame, text=f"    URL: {url}", font=('TkDefaultFont', 8))
            url_label.pack(anchor=tk.W, padx=(20, 0))

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Status frame
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 5))

        # Status label to show selection count
        status_var = tk.StringVar()
        status_label = ttk.Label(status_frame, textvariable=status_var, font=('TkDefaultFont', 9))
        status_label.pack()

        def update_status():
            selected_count = sum(1 for var in selection_vars if var.get())
            status_var.set(f"Selected: {selected_count} of {len(model_urls)} models")

        # Update status when checkboxes change
        for var in selection_vars:
            var.trace('w', lambda *args: update_status())

        # Initial status update
        update_status()

        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))

        # Buttons
        def select_all():
            for var in selection_vars:
                var.set(True)

        def select_none():
            for var in selection_vars:
                var.set(False)

        def select_recommended():
            for i, var in enumerate(selection_vars):
                url = model_urls[i]
                url_lower = url.lower()
                # Select if matches package type or is SOT23 (common default)
                if package_type and package_type.lower() in url_lower:
                    var.set(True)
                elif 'sot23' in url_lower and not package_type:
                    var.set(True)
                else:
                    var.set(False)

        def confirm_selection():
            selected = []
            for i, var in enumerate(selection_vars):
                if var.get():
                    selected.append(model_urls[i])

            if not selected:
                messagebox.showwarning("No Selection", "Please select at least one 3D model to download, or click 'Skip All' to continue without downloading.")
                return

            nonlocal selected_urls
            selected_urls = selected
            dialog.destroy()

        def cancel_selection():
            nonlocal selected_urls
            selected_urls = []
            dialog.destroy()

        def skip_all():
            nonlocal selected_urls
            selected_urls = []
            dialog.destroy()

        # Selection buttons (left side)
        selection_frame = ttk.Frame(button_frame)
        selection_frame.pack(side=tk.LEFT)

        ttk.Button(selection_frame, text="Select All", command=select_all).pack(side=tk.LEFT, padx=2)
        ttk.Button(selection_frame, text="Select None", command=select_none).pack(side=tk.LEFT, padx=2)
        ttk.Button(selection_frame, text="Recommended Only", command=select_recommended).pack(side=tk.LEFT, padx=2)

        # Action buttons (right side)
        action_frame = ttk.Frame(button_frame)
        action_frame.pack(side=tk.RIGHT)

        ttk.Button(action_frame, text="Skip All", command=skip_all).pack(side=tk.LEFT, padx=2)
        ttk.Button(action_frame, text="Cancel Search", command=cancel_selection).pack(side=tk.LEFT, padx=2)

        # Make the download button more prominent
        download_btn = ttk.Button(action_frame, text="✅ Download Selected", command=confirm_selection)
        download_btn.pack(side=tk.LEFT, padx=5)

        # Set focus to download button
        download_btn.focus_set()

        # Add keyboard shortcuts
        dialog.bind('<Return>', lambda e: confirm_selection())
        dialog.bind('<Escape>', lambda e: skip_all())

        # Wait for dialog to close
        dialog.wait_window()

        self.add_comment(f"📋 User selected {len(selected_urls)} out of {len(model_urls)} models")
        return selected_urls

    def show_final_results(self, manufacturer, part_number, package_type, datasheet_file, model_files):
        """Show final search results"""
        self.add_comment("")
        self.add_comment("=" * 50)
        self.add_comment("🎉 SEARCH COMPLETE")
        self.add_comment("=" * 50)
        self.add_comment(f"Manufacturer: {manufacturer}")
        self.add_comment(f"Part Number: {part_number}")
        self.add_comment(f"Package Type: {package_type or 'Unknown'}")
        self.add_comment("")

        if datasheet_file:
            self.add_comment(f"✅ Datasheet: {datasheet_file}")
        else:
            self.add_comment("❌ Datasheet: Not found")

        if model_files:
            self.add_comment(f"✅ 3D Models: {len(model_files)} found")
            for model in model_files:
                self.add_comment(f"   - {model}")
        else:
            self.add_comment("❌ 3D Models: Not found")

        success = bool(datasheet_file or model_files)
        if success:
            self.add_comment("🎉 Search successful!")
        else:
            self.add_comment("⚠️ Search completed with limited results")

    def search_complete(self):
        """Called when search is complete"""
        self.progress.stop()
        self.search_button.config(state='normal')
        self.status_var.set("Ready")

        # Set final status based on what was found
        pdf_found = bool(self.pdf_file_text.get() and self.pdf_file_text.get() != "Not found")
        step_found = bool(self.step_file_text.get() and self.step_file_text.get() != "Not found")

        if pdf_found and step_found:
            self.status_text.set("✅ Success - Found datasheet and 3D model")
        elif pdf_found:
            self.status_text.set("⚠️ Partial success - Found datasheet only")
        elif step_found:
            self.status_text.set("⚠️ Partial success - Found 3D model only")
        else:
            self.status_text.set("❌ No files found")

    def manual_part_url_input(self, manufacturer, part_number, website_url):
        """Allow user to manually provide the part page URL"""
        part_url = simpledialog.askstring(
            "Manual Part URL",
            f"Please navigate to {website_url} and find the page for part {part_number}.\n\nEnter the full URL of the part page:",
            initialvalue=website_url
        )

        if part_url:
            self.add_comment(f"📝 User provided part URL: {part_url}")

            # Test the part URL
            try:
                response = self.session.get(part_url, timeout=20)
                if response.status_code == 200:
                    self.add_comment("✅ Part URL is accessible")

                    # Learn from this manual input
                    manufacturer_key = manufacturer.lower().strip()
                    base_url = f"{urlparse(website_url).scheme}://{urlparse(website_url).netloc}"

                    if manufacturer_key not in self.knowledge_base:
                        self.learn_new_manufacturer(manufacturer, base_url, part_url, "MANUAL")

                    # Process the part page
                    self.process_part_page(response.text, part_url, manufacturer, part_number)
                else:
                    self.add_comment(f"❌ Part URL returned error: {response.status_code}")

            except Exception as e:
                self.add_comment(f"❌ Cannot access part URL: {e}")

    def extract_part_page_from_search(self, search_html, part_number, base_url):
        """Extract part page URL from search results"""
        soup = BeautifulSoup(search_html, 'html.parser')

        base_part = part_number.split('-')[0]
        part_family = base_part[:-2] if base_part[-2:].isdigit() else base_part

        # Look for links that might lead to the part page
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().strip()

            if (part_family.lower() in text.lower() or
                part_family.lower() in href.lower() or
                part_number.lower() in text.lower()):

                full_url = urljoin(base_url, href)

                try:
                    response = self.session.get(full_url, timeout=20)
                    if response.status_code == 200:
                        if part_number.lower() in response.text.lower():
                            self.add_comment(f"✅ Found part page: {full_url}")
                            return response.text, full_url
                except Exception as e:
                    continue

        return None, None

    def process_part_page(self, html, part_page_url, manufacturer, part_number):
        """Process the part page to extract datasheet and 3D models"""
        self.add_comment(f"📄 Processing part page: {part_page_url}")

        # Clean up any existing files for this part before downloading new ones
        self._cleanup_existing_files(manufacturer, part_number)

        # Extract package type
        package_type = self.extract_package_type(html, part_number)
        if package_type:
            self.add_comment(f"📦 Package type: {package_type}")
        else:
            self.add_comment("⚠️ Could not determine package type")

        # Extract and download datasheet
        datasheet_url = self.extract_datasheet_link(html, part_page_url)
        datasheet_file = None

        if datasheet_url:
            self.add_comment(f"📄 Found datasheet: {datasheet_url}")
            self.status_text.set("Downloading datasheet...")
            # Add manufacturer name to filename
            manufacturer_clean = manufacturer.replace(" ", "_").replace(".", "").replace(",", "")
            datasheet_filename = f"{manufacturer_clean} {part_number}_datasheet.pdf"
            datasheet_file = self.download_file(datasheet_url, self.datasheet_dir, datasheet_filename)
            if datasheet_file:
                self.add_comment(f"✅ Datasheet saved: {datasheet_file}")
                self.pdf_file_text.set(os.path.basename(datasheet_file))
            else:
                self.pdf_file_text.set("Download failed")
        else:
            self.add_comment("❌ No datasheet found")
            self.pdf_file_text.set("Not found")

        # Extract and download 3D models
        model_urls = self.extract_3d_model_links(html, part_page_url, part_number)
        model_files = []

        if model_urls:
            self.add_comment(f"🎯 Found {len(model_urls)} 3D model(s)")
            for i, url in enumerate(model_urls):
                self.add_comment(f"   {i+1}. {url}")

            # Find the correct model that matches the package type
            correct_model_url = None
            manufacturer_clean = manufacturer.replace(" ", "_").replace(".", "").replace(",", "")

            if package_type:
                # Look for URL that contains the package type
                for url in model_urls:
                    url_lower = url.lower()
                    package_lower = package_type.lower().replace('-', '')

                    if package_lower in url_lower:
                        correct_model_url = url
                        self.add_comment(f"✅ Found matching model for {package_type}: {url}")
                        break

            # If no package-specific match found, use the first one
            if not correct_model_url and model_urls:
                correct_model_url = model_urls[0]
                self.add_comment(f"⚠️ No package-specific match, using first model: {correct_model_url}")

            # Download the correct model
            if correct_model_url:
                self.status_text.set("Downloading 3D model...")
                model_filename = f"{manufacturer_clean} {part_number}_{package_type or 'unknown'}.step"
                model_file = self.download_file(correct_model_url, self.model_3d_dir, model_filename)
                if model_file:
                    model_files.append(model_file)
                    self.add_comment(f"✅ 3D model saved: {model_file}")
                    self.step_file_text.set(os.path.basename(model_file))
                else:
                    self.step_file_text.set("Download failed")
            else:
                self.step_file_text.set("No suitable model found")
        else:
            self.add_comment("❌ No 3D models found")
            self.step_file_text.set("Not found")

        # Learn from successful extractions
        if datasheet_url or model_urls:
            self.learn_from_successful_extraction(manufacturer, datasheet_url, model_urls, package_type)

        # Log the found files to CSV
        success = bool(datasheet_file or model_files)
        model_url = correct_model_url if 'correct_model_url' in locals() else None
        model_filename = os.path.basename(model_files[0]) if model_files else None
        datasheet_filename = os.path.basename(datasheet_file) if datasheet_file else None

        self._log_found_files(
            manufacturer=manufacturer,
            part_number=part_number,
            datasheet_url=datasheet_url,
            datasheet_filename=datasheet_filename,
            model_url=model_url,
            model_filename=model_filename,
            package_type=package_type,
            success=success
        )

        # Show final results
        self.show_final_results(manufacturer, part_number, package_type, datasheet_file, model_files)

    def extract_package_type(self, html, part_number):
        """Extract package type from part page"""
        if not html:
            return None

        soup = BeautifulSoup(html, 'html.parser')

        # Look for package in table row for this specific part
        for row in soup.find_all('tr'):
            row_text = row.get_text()
            if part_number in row_text:
                cells = row.find_all(['td', 'th'])
                for cell in cells:
                    cell_text = cell.get_text().strip()
                    common_packages = ['SOT23', 'SOT-23', 'SOT323', 'SOT-323', 'SOT25', 'SOT-25',
                                     'SC59', 'SC-59', 'SC70', 'SC-70', 'SOIC', 'QFN', 'BGA', 'DIP']

                    if cell_text in common_packages:
                        return cell_text

        # Fallback: search in general content
        html_lower = html.lower()
        packages = ['sot-23', 'sot23', 'sot-323', 'sot323', 'sot-25', 'sot25', 'sc-59', 'sc59']
        for pkg in packages:
            if pkg in html_lower:
                return pkg.upper().replace('SOT', 'SOT-').replace('SC', 'SC-')

        return None

    def extract_datasheet_link(self, html, base_url):
        """Extract datasheet download link"""
        if not html:
            return None

        soup = BeautifulSoup(html, 'html.parser')

        # Look for datasheet links
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().lower()

            if (href.endswith('.pdf') and
                any(keyword in text for keyword in ['datasheet', 'data sheet'])):
                return urljoin(base_url, href)

        # Try regex patterns
        patterns = [
            r'href="([^"]*\.pdf[^"]*)"[^>]*>.*?datasheet',
            r'href="([^"]*datasheet[^"]*\.pdf)"'
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, html, re.IGNORECASE)
            for match in matches:
                link = match.group(1)
                return urljoin(base_url, link)

        return None

    def extract_3d_model_links(self, html, base_url, part_number):
        """Extract 3D model download links"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')
        model_links = []

        # Look for STEP/STP file links
        for link in soup.find_all('a', href=True):
            href = link['href']
            if any(ext in href.lower() for ext in ['.step', '.stp']):
                full_url = urljoin(base_url, href)
                model_links.append(full_url)

        return model_links

    def load_excel_file(self):
        """Load Excel file and set up matrix search"""
        try:
            # Check if required packages are available
            try:
                import pandas as pd
                import openpyxl
            except ImportError as e:
                missing_pkg = str(e).split("'")[1] if "'" in str(e) else "pandas/openpyxl"
                self.add_comment(f"❌ Missing package: {missing_pkg}")
                messagebox.showerror("Missing Package",
                    f"Excel support requires additional packages.\n"
                    f"Please install: pip install pandas openpyxl xlrd\n\n"
                    f"Missing: {missing_pkg}")
                return

            # File selection dialog
            file_path = filedialog.askopenfilename(
                title="Select Excel File",
                filetypes=[
                    ("Excel files", "*.xlsx *.xls"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            self.add_comment(f"📊 Loading Excel file: {file_path}")

            # Load Excel file into pandas DataFrame
            try:
                # Try to read as xlsx first, then xls
                if file_path.endswith('.xlsx'):
                    self.excel_df = pd.read_excel(file_path, engine='openpyxl')
                else:
                    self.excel_df = pd.read_excel(file_path, engine='xlrd')

                # Store file path
                self.excel_file_path = file_path

                # Display file info
                rows, cols = self.excel_df.shape
                self.add_comment(f"✅ Loaded Excel file: {rows} rows, {cols} columns")

                # Show column headers for reference
                self.add_comment(f"📋 Columns: {list(self.excel_df.columns)}")

                # Enable Excel part file search mode
                self.matrix_search_enabled = True
                self.add_comment("🔍 Excel part file search mode enabled!")
                self.add_comment("📊 Column mapping: F=Manufacturer, G=Part Number, H=Datasheet, J=Mfg(STEP), K=STEP File")

                # Add matrix search button
                if not hasattr(self, 'matrix_search_button'):
                    button_frame = self.search_button.master
                    self.matrix_search_button = ttk.Button(button_frame, text="🔍 Excel Part File Search", command=self.start_matrix_search)
                    self.matrix_search_button.pack(side=tk.LEFT, padx=5)

            except Exception as e:
                self.add_comment(f"❌ Error reading Excel file: {str(e)}")
                messagebox.showerror("Error", f"Could not read Excel file:\n{str(e)}")

        except Exception as e:
            self.add_comment(f"❌ Error loading Excel file: {str(e)}")

    def start_matrix_search(self):
        """Start matrix-based search"""
        if not hasattr(self, 'excel_df') or self.excel_df is None:
            messagebox.showerror("Error", "Please load an Excel file first!")
            return

        # Ask user what type of search they want
        choice = messagebox.askyesnocancel(
            "Excel Part File Search Type",
            "Choose search type:\n\n"
            "YES = Process ALL parts in Excel file\n"
            "NO = Process single part only\n"
            "CANCEL = Cancel search"
        )

        if choice is None:  # Cancel
            return

        # Disable buttons and start progress
        self.search_button.config(state='disabled')
        self.matrix_search_button.config(state='disabled')
        self.progress.start()

        if choice:  # YES - Process all parts
            self.status_text.set("Processing all parts in matrix...")
            search_thread = threading.Thread(target=self.process_all_matrix_parts)
            search_thread.daemon = True
            search_thread.start()
        else:  # NO - Process single part
            manufacturer = self.manufacturer_var.get().strip()
            part_number = self.part_number_var.get().strip()

            if not part_number:
                messagebox.showerror("Error", "Please enter a part number")
                self.search_button.config(state='normal')
                self.matrix_search_button.config(state='normal')
                self.progress.stop()
                return

            self.status_text.set("Matrix searching single part...")
            search_thread = threading.Thread(target=self.matrix_search_component, args=(manufacturer, part_number))
            search_thread.daemon = True
            search_thread.start()

    def matrix_search_component(self, manufacturer, part_number):
        """Search for component in Excel matrix"""
        try:
            self.add_comment(f"🔍 Matrix search for {manufacturer} {part_number}")

            # Search for part number in the matrix
            part_found = False
            found_row = None

            # First, try to find data by scanning columns
            self.add_comment("📊 Scanning matrix for data...")

            # Look for part number in column G (index 6, 0-based)
            if len(self.excel_df.columns) > 6:  # Make sure column G exists
                col_g = self.excel_df.iloc[:, 6]  # Column G (Part Number)

                # Search for exact match first
                matches = self.excel_df[col_g.astype(str).str.contains(part_number, case=False, na=False)]

                if not matches.empty:
                    found_row = matches.index[0]  # Get first match
                    part_found = True
                    self.add_comment(f"✅ Found part {part_number} in row {found_row + 2}")  # +2 for Excel row numbering

            if not part_found:
                # If not found in column G, search all columns
                self.add_comment("🔍 Part not found in column G, searching all columns...")
                for col_idx, column in enumerate(self.excel_df.columns):
                    col_data = self.excel_df.iloc[:, col_idx]
                    matches = self.excel_df[col_data.astype(str).str.contains(part_number, case=False, na=False)]

                    if not matches.empty:
                        found_row = matches.index[0]
                        part_found = True
                        self.add_comment(f"✅ Found part {part_number} in column {column} (index {col_idx}), row {found_row + 2}")
                        break

            if part_found:
                self.process_matrix_row(found_row, manufacturer, part_number)
            else:
                self.add_comment(f"❌ Part {part_number} not found in matrix")

        except Exception as e:
            self.add_comment(f"❌ Matrix search error: {str(e)}")

        finally:
            # Re-enable buttons and stop progress
            self.search_button.config(state='normal')
            if hasattr(self, 'matrix_search_button'):
                self.matrix_search_button.config(state='normal')
            self.progress.stop()
            self.status_text.set("Matrix search completed")

    def process_all_matrix_parts(self):
        """Process all parts in the matrix sequentially"""
        try:
            import pandas as pd

            self.add_comment("🔍 Starting to process ALL parts in matrix...")

            total_rows = len(self.excel_df)
            processed_count = 0
            found_count = 0
            missing_datasheet_count = 0
            missing_step_count = 0

            # Initialize stop flag
            self.stop_all_processing = False

            # Process each row in the matrix
            for row_idx in range(total_rows):
                # Check if user cancelled all processing
                if hasattr(self, 'stop_all_processing') and self.stop_all_processing:
                    self.add_comment("🛑 Processing cancelled by user")
                    break

                try:
                    row_data = self.excel_df.iloc[row_idx]

                    # Get part number from column G (index 6)
                    col_g_part = row_data.iloc[6] if len(row_data) > 6 else ""

                    # Skip rows with no part number
                    if pd.isna(col_g_part) or str(col_g_part).strip() == "":
                        continue

                    # Get manufacturer from column F (index 5)
                    col_f_mfg = row_data.iloc[5] if len(row_data) > 5 else ""

                    part_number = str(col_g_part).strip()
                    manufacturer = str(col_f_mfg).strip() if not pd.isna(col_f_mfg) else "Unknown"

                    self.add_comment(f"\n📊 Processing Row {row_idx + 2}: {manufacturer} {part_number}")

                    # Update GUI fields with current part being processed
                    self.manufacturer_var.set(manufacturer)
                    self.part_number_var.set(part_number)
                    self.root.update()  # Refresh GUI to show current part

                    # Process this row
                    self.process_matrix_row(row_idx, manufacturer, part_number)

                    processed_count += 1
                    found_count += 1

                    # Update status
                    self.status_text.set(f"Processed {processed_count} parts...")

                    # Small delay to prevent overwhelming the system
                    import time
                    time.sleep(0.1)

                except Exception as e:
                    self.add_comment(f"❌ Error processing row {row_idx + 2}: {str(e)[:50]}")
                    continue

            # Final summary
            self.add_comment(f"\n🎉 Matrix processing complete!")
            self.add_comment(f"📊 Total rows processed: {processed_count}")
            self.add_comment(f"✅ Parts found: {found_count}")

        except Exception as e:
            self.add_comment(f"❌ Error processing matrix: {str(e)}")

        finally:
            # Re-enable buttons and stop progress
            self.search_button.config(state='normal')
            if hasattr(self, 'matrix_search_button'):
                self.matrix_search_button.config(state='normal')
            self.progress.stop()
            self.status_text.set("All matrix processing completed")

    def process_matrix_row(self, row_idx, manufacturer, part_number):
        """Process found row in matrix - check and update columns H and K"""
        try:
            import pandas as pd  # Import here to avoid global dependency

            row_data = self.excel_df.iloc[row_idx]

            # Get values from specific columns
            col_f_mfg = row_data.iloc[5] if len(row_data) > 5 else ""  # Column F (Manufacturer)
            col_g_part = row_data.iloc[6] if len(row_data) > 6 else ""  # Column G (Part Number)
            col_h_datasheet = row_data.iloc[7] if len(row_data) > 7 else ""  # Column H (Datasheet)
            col_j_step_mfg = row_data.iloc[9] if len(row_data) > 9 else ""  # Column J (STEP Manufacturer)
            col_k_step = row_data.iloc[10] if len(row_data) > 10 else ""  # Column K (STEP File)

            self.add_comment(f"📊 Row {row_idx + 2}: Mfg='{col_f_mfg}', Part='{col_g_part}'")
            self.add_comment(f"📊 Datasheet='{col_h_datasheet}', STEP='{col_k_step}'")

            # Check if datasheet is missing (column H is blank)
            datasheet_needed = pd.isna(col_h_datasheet) or str(col_h_datasheet).strip() == ""
            step_needed = pd.isna(col_k_step) or str(col_k_step).strip() == ""

            # Search for files
            files_found = []

            # Use enhanced datasheet finder
            self.add_comment(f"📄 Using enhanced datasheet finder for {manufacturer} {part_number}...")
            datasheet_result = self.search_datasheet_enhanced(manufacturer, part_number)

            if datasheet_result and datasheet_result.get('success'):
                self.add_comment(f"✅ Found datasheet via {datasheet_result.get('source', 'enhanced finder')}")

                # Extract filename from path
                datasheet_file = datasheet_result.get('datasheet_file', '')
                if datasheet_file:
                    filename = os.path.basename(datasheet_file)
                    self.add_comment(f"📄 Downloaded: {filename}")

                    # Only update Excel if datasheet was missing
                    if datasheet_needed:
                        self.update_matrix_cell(row_idx, 'H', f"DATA-SHEETS\\{filename}")
                        self.add_comment(f"💾 Updated matrix with new datasheet location")
                    else:
                        self.add_comment(f"📋 Matrix already has datasheet: {col_h_datasheet} (not updating)")

                    # Show package information if available
                    if datasheet_result.get('pdf_parsing') and datasheet_result['pdf_parsing'].get('package_info'):
                        package_info = datasheet_result['pdf_parsing']['package_info']
                        if package_info.get('package'):
                            self.add_comment(f"📦 Package type: {package_info['package']}")

                    # Show incomplete part warning if applicable
                    if datasheet_result.get('incomplete_part'):
                        self.add_comment("⚠️ WARNING: This appears to be an incomplete part number")
                        self.add_comment("💡 Complete part numbers usually have suffixes like -PU, -AU, -ND, etc.")

                    files_found.append("datasheet")
                else:
                    self.add_comment("⚠️ Datasheet found but no file downloaded")
            else:
                self.add_comment("❌ Enhanced datasheet finder failed")
                if datasheet_needed:
                    # Ask for help finding datasheet
                    if self.ask_for_help_finding_file(manufacturer, part_number, "datasheet"):
                        return  # Stop processing to allow manual intervention
                else:
                    self.add_comment(f"📋 Will use existing datasheet from matrix: {col_h_datasheet}")

            if step_needed:
                self.add_comment(f"🔧 STEP file missing for {manufacturer} {part_number} - using enhanced 3D finder...")
                # Use the enhanced 3D finder
                step_result_enhanced = self.search_step_enhanced(manufacturer, part_number)

                if step_result_enhanced and step_result_enhanced.get('success'):
                    # Convert to expected format for matrix update
                    step_file = step_result_enhanced.get('step_file', '')
                    source = step_result_enhanced.get('source', 'Enhanced 3D Finder')

                    if step_file:
                        filename = os.path.basename(step_file)
                        step_result = (step_file, filename, source)
                    else:
                        step_result = None
                else:
                    # Fallback to existing methods for specific manufacturers
                    self.add_comment("🔄 Enhanced 3D finder failed, trying manufacturer-specific methods...")
                    step_result = None

                    if 'wurth' in manufacturer.lower() or 'würth' in manufacturer.lower():
                        step_result_fallback = self.search_wurth_simple(manufacturer, part_number)
                        if step_result_fallback:
                            filename = f"{part_number}.step"
                            step_result = ("wurth_simple", filename, "WURTH")
                    elif ('i-pex' in manufacturer.lower() or 'ipex' in manufacturer.lower() or
                          'hirose' in manufacturer.lower()):
                        step_result_fallback = self.search_ipex_simple(manufacturer, part_number)
                        if step_result_fallback:
                            filename = f"{part_number}.step"
                            step_result = ("ipex_simple", filename, "Hirose")
                    elif ('diodes' in manufacturer.lower()):
                        step_result_fallback = self.search_diodes_comprehensive(manufacturer, part_number)
                        if step_result_fallback:
                            filename = f"{part_number}.step"
                            step_result = ("diodes_comprehensive", filename, "Diodes Inc")
                    else:
                        # Try alternative STEP sources as final fallback
                        step_result = self.search_alternative_step_sources(manufacturer, part_number)
                if step_result:
                    # Handle different return formats
                    if len(step_result) == 3:
                        step_url, filename, source = step_result
                        self.add_comment(f"✅ Found STEP file: {filename}")
                        # Update Excel with the filename and source
                        self.update_matrix_cell(row_idx, 'K', filename)
                        self.update_matrix_cell(row_idx, 'J', f"{manufacturer} ({source})")
                    elif len(step_result) == 2:
                        step_url, filename = step_result
                        self.add_comment(f"✅ Found STEP file: {filename}")
                        # Update Excel with the filename and manufacturer
                        self.update_matrix_cell(row_idx, 'K', filename)
                        self.update_matrix_cell(row_idx, 'J', manufacturer)
                    files_found.append("step")
                else:
                    self.add_comment("❌ STEP file not found on manufacturer website")
                    # Try alternative STEP file sources
                    alt_step_result = self.search_alternative_step_sources(manufacturer, part_number)
                    if alt_step_result:
                        step_url, filename, source = alt_step_result
                        self.add_comment(f"✅ Found STEP file on {source}: {filename}")
                        # Update Excel with the filename and source
                        self.update_matrix_cell(row_idx, 'K', f"{filename} (from {source})")
                        self.update_matrix_cell(row_idx, 'J', f"{manufacturer} ({source})")
                        files_found.append("step")
                    else:
                        # Ask for help finding STEP file
                        if self.ask_for_help_finding_file(manufacturer, part_number, "step"):
                            return  # Stop processing to allow manual intervention
            else:
                self.add_comment(f"✅ STEP file already exists: {col_k_step}")

            # Summary
            if not datasheet_needed and not step_needed:
                self.add_comment("🎉 Both datasheet and STEP file are already available!")
            elif files_found:
                self.add_comment(f"🎉 Successfully found and downloaded {len(files_found)} missing files!")
            else:
                if datasheet_needed and step_needed:
                    self.add_comment("❌ Could not find datasheet or STEP file")
                elif datasheet_needed:
                    self.add_comment("❌ Could not find datasheet")
                else:
                    self.add_comment("❌ Could not find STEP file")

        except Exception as e:
            self.add_comment(f"❌ Error processing matrix row: {str(e)}")

    def update_matrix_cell(self, row_idx, col_letter, value):
        """Update specific cell in Excel matrix"""
        try:
            # Convert column letter to index (H=7, J=9, K=10 in 0-based indexing)
            col_map = {'H': 7, 'J': 9, 'K': 10}
            col_idx = col_map.get(col_letter.upper())

            if col_idx is not None and col_idx < len(self.excel_df.columns):
                # Update DataFrame
                self.excel_df.iloc[row_idx, col_idx] = value

                # Save back to Excel file
                if hasattr(self, 'excel_file_path'):
                    try:
                        if self.excel_file_path.endswith('.xlsx'):
                            self.excel_df.to_excel(self.excel_file_path, index=False, engine='openpyxl')
                        else:
                            self.excel_df.to_excel(self.excel_file_path, index=False)

                        self.add_comment(f"💾 Updated Excel cell {col_letter}{row_idx + 2} = '{value}'")
                    except PermissionError:
                        self.add_comment(f"⚠️ Excel file is open - cannot update cell {col_letter}{row_idx + 2}")
                        self.add_comment(f"   💡 Please close Excel file to save updates automatically")
                        self.add_comment(f"   📝 Manual update needed: {col_letter}{row_idx + 2} = '{value}'")

        except Exception as e:
            self.add_comment(f"❌ Error updating matrix: {str(e)}")

    def web_search_for_file(self, manufacturer, part_number, file_type):
        """Search web for specific file type (datasheet or step) and download it"""
        try:
            self.add_comment(f"   🔍 Searching for {file_type}...")

            if file_type == "datasheet":
                # Use the enhanced datasheet finder system
                return self.search_datasheet_enhanced(manufacturer, part_number)
            elif file_type == "step":
                # For STEP files, use existing system with distributor fallback
                return self.search_step_files_existing(manufacturer, part_number)

        except Exception as e:
            self.add_comment(f"❌ Error in web search: {str(e)[:50]}")
            logger.error(f"Web search error: {e}")

        return None

    def search_datasheet_enhanced(self, manufacturer, part_number):
        """Enhanced datasheet search using the new combined system"""
        try:
            self.add_comment(f"🚀 Using enhanced datasheet finder for {manufacturer} {part_number}")

            # Import and use the enhanced datasheet finder
            from datasheet_finder import find_datasheet_combined

            # Use non-interactive mode for GUI integration
            result = find_datasheet_combined(manufacturer, part_number, interactive=False)

            if result['success']:
                self.add_comment(f"✅ Found datasheet via {result['source']}")

                # Update GUI status
                if result.get('datasheet_file'):
                    self.pdf_file_text.set(result['datasheet_file'])
                    self.add_comment(f"📄 Downloaded: {result['datasheet_file']}")

                # Show package information if available
                if result.get('pdf_parsing') and result['pdf_parsing'].get('package_info'):
                    package_info = result['pdf_parsing']['package_info']
                    if package_info.get('package'):
                        self.add_comment(f"📦 Package type: {package_info['package']}")

                # Show incomplete part warning if applicable
                if result.get('incomplete_part'):
                    self.add_comment("⚠️ WARNING: This appears to be an incomplete part number")
                    self.add_comment("💡 Complete part numbers usually have suffixes like -PU, -AU, -ND, etc.")

                # Show similar parts if found
                if (result.get('pdf_parsing') and
                    result['pdf_parsing'].get('part_validation') and
                    result['pdf_parsing']['part_validation'].get('found_parts')):
                    similar_parts = result['pdf_parsing']['part_validation']['found_parts']
                    if similar_parts:
                        self.add_comment(f"📋 Similar parts found: {', '.join(similar_parts[:3])}")

                # Return format expected by the main program
                safe_manufacturer = re.sub(r'[^\w\s-]', '', manufacturer).strip()
                safe_part = re.sub(r'[^\w\s-]', '', part_number).strip()
                filename = f"{safe_manufacturer} {safe_part}_datasheet.pdf"
                return (result.get('datasheet_file', ''), filename)
            else:
                self.add_comment(f"❌ Enhanced datasheet finder failed: {result['message']}")
                self.add_comment(f"🔄 Attempts made: {result['attempt_count']}")
                return None

        except ImportError:
            self.add_comment("⚠️ Enhanced datasheet finder not available, using fallback")
            return self.search_datasheet_fallback(manufacturer, part_number)
        except Exception as e:
            self.add_comment(f"❌ Error in enhanced datasheet search: {str(e)[:50]}")
            return self.search_datasheet_fallback(manufacturer, part_number)

    def search_step_files_existing(self, manufacturer, part_number):
        """Search for STEP files using existing system"""
        try:
            # Step 1: Try distributors first (Digi-Key, Mouser) but keep original manufacturer
            distributor_result = self.search_distributors_for_part_info(manufacturer, part_number)

            if distributor_result:
                verified_manufacturer, website, datasheet_url = distributor_result
                # Keep the original manufacturer from Excel, don't use the "verified" one
                self.add_comment(f"   Using manufacturer from Excel: {manufacturer} (not {verified_manufacturer})")

                # Search manufacturer website for STEP file
                if 'wurth' in verified_manufacturer.lower() or 'würth' in verified_manufacturer.lower():
                    # Use the WORKING simple WURTH search method
                    self.add_comment(f"   🔍 Using working WURTH simple search...")
                    success = self.search_wurth_simple(verified_manufacturer, part_number)
                    if success:
                        # The simple search downloads the file, so we need to return a result
                        filename = f"{part_number}.step"  # Generic filename
                        return ("wurth_simple_search", filename, "WURTH")
                else:
                    # Try other manufacturer searches
                    step_result = self.search_manufacturer_for_step(verified_manufacturer, part_number, website)
                    if step_result:
                        return step_result

            # Step 2: If distributors failed, try direct manufacturer search
            # Try to find manufacturer website
            manufacturer_key = manufacturer.lower().strip()
            if hasattr(self, 'manufacturer_websites') and manufacturer_key in self.manufacturer_websites:
                website = self.manufacturer_websites[manufacturer_key]
                step_result = self.search_manufacturer_for_step(manufacturer, part_number, website)
                if step_result:
                    return step_result

            return None

        except Exception as e:
            self.add_comment(f"   ❌ Web search error: {str(e)[:50]}")
            return None



    def search_step_enhanced(self, manufacturer, part_number):
        """Enhanced 3D STEP file search using the new modular system"""
        try:
            self.add_comment(f"🚀 Using enhanced 3D finder for {manufacturer} {part_number}")

            # Import and use the enhanced 3D finder
            from step_finder import find_step_file

            result = find_step_file(manufacturer, part_number)

            if result['success']:
                self.add_comment(f"✅ Found STEP file via {result['source']}")
                self.add_comment(f"📁 Downloaded: {result['step_file']}")
                return result
            else:
                self.add_comment(f"❌ Enhanced 3D finder failed: {result['message']}")
                return result

        except ImportError:
            self.add_comment("⚠️ Enhanced 3D finder not available, using fallback")
            return {'success': False, 'message': 'Enhanced 3D finder not available'}
        except Exception as e:
            self.add_comment(f"❌ Error in enhanced 3D search: {str(e)[:50]}")
            return {'success': False, 'message': f'Error: {str(e)}'}

    def search_wurth_for_step(self, manufacturer, part_number):
        """Search WURTH website with learning system"""
        try:
            search_url = f"https://www.we-online.com/en/components/products?sq={part_number}"

            # First, try learned patterns for WURTH
            learned_result = self.apply_learned_patterns("WURTH", part_number, search_url)
            if learned_result:
                return learned_result

            # Try Selenium-based search (proper search engine interaction)
            selenium_result = self._search_wurth_with_selenium(manufacturer, part_number)
            if selenium_result:
                return selenium_result

            # Fallback to URL-based search
            auto_result = self._search_wurth_automatic(manufacturer, part_number, search_url)
            if auto_result:
                return auto_result

            # If part found but download failed, enter WURTH-specific learning mode
            if self.learning_mode and self._part_found_on_wurth(part_number, search_url):
                self.add_comment(f"   🎓 WURTH part found but download failed - entering WURTH learning mode")
                return self.learn_wurth_download_pattern(part_number, search_url)

            return None

        except Exception as e:
            self.add_comment(f"   ❌ WURTH search error: {str(e)[:50]}")
            return None

    def _search_wurth_automatic(self, manufacturer, part_number, search_url):
        """Enhanced WURTH search using their actual search engine interface"""
        try:
            self.add_comment(f"   🔍 Using WURTH search engine interface for {part_number}")

            import time
            time.sleep(2)  # Be respectful to WURTH servers

            # Step 1: Go to WURTH main page first to get search form
            wurth_main_url = "https://www.we-online.com/en"
            self.add_comment(f"   📄 Loading WURTH main page...")

            response = self.session.get(wurth_main_url, timeout=30)
            if response.status_code != 200:
                self.add_comment(f"   ❌ Could not load WURTH main page: HTTP {response.status_code}")
                return None

            # Step 2: Try to use their search form (simulate typing in search box)
            # This is a simplified approach - real search engine interaction would need Selenium
            self.add_comment(f"   🔍 Attempting WURTH search form submission...")

            # Try the search URL approach as fallback
            response = self.session.get(search_url, timeout=30, allow_redirects=True)

            if response.status_code != 200:
                self.add_comment(f"   ❌ WURTH search failed: HTTP {response.status_code}")
                return None

            soup = BeautifulSoup(response.text, 'html.parser')
            page_text = soup.get_text().lower()
            final_url = response.url

            self.add_comment(f"   📄 WURTH search page loaded: {final_url[:50]}...")

            # Check if part number appears in search results
            if part_number.lower() not in page_text and part_number.lower() not in final_url.lower():
                self.add_comment(f"   ❌ Part {part_number} not found in WURTH search results")
                return None

            self.add_comment(f"   ✅ Found {part_number} in WURTH search results")

            # Step 2: Look for product page link or direct STEP file download
            product_links = []
            step_download_links = []

            for link in soup.find_all('a', href=True):
                href = link['href']
                link_text = link.get_text().strip().lower()

                # Make URL absolute
                if href.startswith('http'):
                    full_url = href
                elif href.startswith('/'):
                    full_url = urljoin('https://www.we-online.com', href)
                else:
                    continue

                # Look for direct STEP file downloads
                if (any(ext in full_url.lower() for ext in ['.step', '.stp']) or
                    any(keyword in link_text for keyword in ['step', '3d model', 'cad', 'download step'])):
                    step_download_links.append((full_url, link_text))
                    self.add_comment(f"   📥 Found potential STEP download: {link_text[:30]}...")

                # Look for product detail pages (where STEP files are usually found)
                elif (part_number.lower() in href.lower() or
                      part_number.lower() in link_text or
                      any(keyword in link_text for keyword in ['details', 'product', 'datasheet'])):
                    product_links.append((full_url, link_text))
                    self.add_comment(f"   🔗 Found product page: {link_text[:30]}...")

            # Step 3: Try direct STEP downloads first
            for step_url, link_text in step_download_links:
                self.add_comment(f"   📥 Trying direct STEP download: {step_url[:50]}...")
                success = self.download_file_from_url(step_url, manufacturer, part_number, "3d_model")
                if success:
                    filename = step_url.split('/')[-1] if '/' in step_url else f"{part_number}.step"
                    self.add_comment(f"   ✅ Successfully downloaded STEP file: {filename}")
                    return (step_url, filename)

            # Step 4: If no direct downloads, try product pages
            for product_url, link_text in product_links[:3]:  # Try first 3 product pages
                self.add_comment(f"   🔍 Checking product page: {product_url[:50]}...")

                try:
                    time.sleep(2)  # Be respectful
                    product_response = self.session.get(product_url, timeout=30)

                    if product_response.status_code == 200:
                        product_soup = BeautifulSoup(product_response.text, 'html.parser')

                        # Look for STEP file download box/button on product page
                        step_selectors = [
                            'a[href*=".step"]',
                            'a[href*=".stp"]',
                            '.step-download',
                            '.cad-download',
                            '.download-step',
                            'a[title*="STEP"]',
                            'a[title*="3D"]',
                            '.btn-step',
                            '[data-file-type="step"]'
                        ]

                        for selector in step_selectors:
                            elements = product_soup.select(selector)
                            for element in elements:
                                step_href = element.get('href')
                                if step_href:
                                    # Make URL absolute
                                    if step_href.startswith('http'):
                                        step_full_url = step_href
                                    elif step_href.startswith('/'):
                                        step_full_url = urljoin('https://www.we-online.com', step_href)
                                    else:
                                        continue

                                    self.add_comment(f"   📥 Found STEP download box: {selector}")
                                    success = self.download_file_from_url(step_full_url, manufacturer, part_number, "3d_model")
                                    if success:
                                        filename = step_full_url.split('/')[-1] if '/' in step_full_url else f"{part_number}.step"
                                        self.add_comment(f"   ✅ Downloaded from product page: {filename}")
                                        return (step_full_url, filename)

                except Exception as e:
                    self.add_comment(f"   ⚠️ Product page error: {str(e)[:30]}")
                    continue

            self.add_comment(f"   ❌ No STEP files found on WURTH for {part_number}")
            return None

        except Exception as e:
            self.add_comment(f"   ❌ WURTH enhanced search error: {str(e)[:50]}")
            return None

    def _part_found_on_wurth(self, part_number, search_url):
        """Quick check if part exists on WURTH"""
        try:
            response = self.session.get(search_url, timeout=15)
            page_text = response.text.lower()
            return (part_number.lower() in page_text or
                   any(keyword in page_text for keyword in ['product', 'component', 'datasheet']))
        except:
            return False

    def search_manufacturer_for_step(self, manufacturer, part_number, website):
        """Search manufacturer website for STEP files"""
        try:
            # This is a placeholder for other manufacturer searches
            # Can be expanded to handle specific manufacturer patterns
            self.add_comment(f"   🔍 Searching {manufacturer} website...")

            # For now, return None - can be expanded later
            return None

        except Exception as e:
            self.add_comment(f"   ❌ Manufacturer search error: {str(e)[:50]}")
            return None

    def search_alternative_step_sources(self, manufacturer, part_number):
        """Search alternative sources for STEP files: UltraLibrarian, SnapMagic, SamacSys"""
        try:
            # Check Selenium availability and inform user
            selenium_available = self._check_selenium_availability()

            # Ask user if they want to search alternative sources
            selenium_info = "\n\n🚀 Enhanced search with Selenium available!" if selenium_available else "\n\n⚠️ Basic search only (install Selenium + ChromeDriver for better results)"

            response = messagebox.askyesno(
                "STEP File Not Found",
                f"STEP file not found on {manufacturer} website for {part_number}.\n\n"
                f"Would you like to search alternative sources?\n\n"
                f"• UltraLibrarian (3D CAD models)\n"
                f"• SnapMagic/SnapEDA (PCB components)\n"
                f"• SamacSys (Component libraries)\n\n"
                f"YES = Search alternative sources\n"
                f"NO = Skip and continue"
                f"{selenium_info}"
            )

            if not response:
                self.add_comment("⏭️ User chose to skip alternative STEP file search")
                return None

            if not selenium_available:
                self.add_comment("⚠️ Selenium not available - using basic search mode")
                self.add_comment("💡 For better results: pip install selenium + install ChromeDriver")
            else:
                self.add_comment("🚀 Using enhanced Selenium search mode")

            self.add_comment("🔍 Searching alternative STEP file sources...")

            # Search each alternative source with specialized learning methods
            sources = [
                ("UltraLibrarian", self.search_ultralibrarian_with_learning),
                ("SnapMagic", self.search_snapmagic_with_learning),
                ("SamacSys", self.search_samacsys_with_learning)
            ]

            for source_name, search_method in sources:
                try:
                    self.add_comment(f"   🔍 Searching {source_name}...")

                    # Retry logic for each source
                    for attempt in range(2):  # Try twice
                        try:
                            result = search_method(manufacturer, part_number)
                            if result:
                                self.add_comment(f"   🎉 Success on {source_name}!")
                                return result  # Return first successful result
                            break  # Don't retry if method completed without error
                        except Exception as e:
                            if attempt == 0:  # First attempt failed
                                self.add_comment(f"   ⚠️ {source_name} attempt {attempt + 1} failed, retrying...")
                                import time
                                time.sleep(3)  # Wait before retry
                            else:  # Second attempt failed
                                self.add_comment(f"   ❌ {source_name} failed after 2 attempts: {str(e)[:30]}")

                except Exception as e:
                    self.add_comment(f"   ❌ {source_name} search error: {str(e)[:30]}")
                    continue

            self.add_comment("❌ No STEP files found on alternative sources")
            return None

        except Exception as e:
            self.add_comment(f"❌ Alternative search error: {str(e)[:50]}")
            return None

    def _check_selenium_availability(self):
        """Check if Selenium and ChromeDriver are available"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options

            # Try to create a Chrome driver instance
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')

            driver = webdriver.Chrome(options=chrome_options)
            driver.quit()
            return True

        except ImportError:
            return False
        except Exception:
            # ChromeDriver not found or other WebDriver issue
            return False

    def _search_wurth_with_selenium(self, manufacturer, part_number):
        """Use Selenium to properly interact with WURTH search engine"""
        try:
            # Try to import Selenium
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.keys import Keys
            from selenium.common.exceptions import TimeoutException, WebDriverException

            self.add_comment(f"   🌐 Using Selenium to interact with WURTH search engine...")

            # Setup Chrome options
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # Run in background
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            driver = webdriver.Chrome(options=chrome_options)

            try:
                # Step 1: Go to WURTH main page
                wurth_url = "https://www.we-online.com/en"
                self.add_comment(f"   📄 Loading WURTH website...")
                driver.get(wurth_url)

                # Wait for page to load
                wait = WebDriverWait(driver, 15)

                # Step 2: Find and use the search box
                search_selectors = [
                    'input[name="sq"]',
                    'input[placeholder*="search"]',
                    'input[placeholder*="Search"]',
                    '.search-input',
                    '#search-input',
                    'input[type="search"]',
                    '.searchfield input',
                    '[data-search-input]'
                ]

                search_input = None
                for selector in search_selectors:
                    try:
                        search_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                        self.add_comment(f"   🔍 Found search box: {selector}")
                        break
                    except TimeoutException:
                        continue

                if not search_input:
                    self.add_comment(f"   ❌ Could not find WURTH search box")
                    return None

                # Step 3: Type the part number in search box
                self.add_comment(f"   ⌨️ Typing '{part_number}' in WURTH search box...")
                search_input.clear()
                search_input.send_keys(part_number)

                # Step 4: Submit the search (try Enter key first, then look for search button)
                try:
                    search_input.send_keys(Keys.RETURN)
                    self.add_comment(f"   🔍 Submitted search with Enter key")
                except:
                    # Try to find and click search button
                    search_button_selectors = [
                        'button[type="submit"]',
                        '.search-button',
                        '.btn-search',
                        'input[type="submit"]',
                        '[data-search-submit]'
                    ]

                    for btn_selector in search_button_selectors:
                        try:
                            search_button = driver.find_element(By.CSS_SELECTOR, btn_selector)
                            search_button.click()
                            self.add_comment(f"   🔍 Clicked search button: {btn_selector}")
                            break
                        except:
                            continue

                # Step 5: Wait for search results
                import time
                time.sleep(3)  # Give time for search results to load

                # Step 6: Look for product links in search results
                self.add_comment(f"   📋 Looking for {part_number} in search results...")

                # Check if part number appears in results
                page_source = driver.page_source.lower()
                if part_number.lower() not in page_source:
                    self.add_comment(f"   ❌ {part_number} not found in WURTH search results")
                    return None

                self.add_comment(f"   ✅ Found {part_number} in WURTH search results")

                # Step 7: Look for product detail links
                product_link_selectors = [
                    f'a[href*="{part_number.lower()}"]',
                    '.product-link',
                    '.result-link',
                    'a[title*="' + part_number + '"]',
                    '.product-title a',
                    '.search-result a'
                ]

                for link_selector in product_link_selectors:
                    try:
                        product_links = driver.find_elements(By.CSS_SELECTOR, link_selector)
                        for link in product_links[:3]:  # Try first 3 links
                            try:
                                product_url = link.get_attribute('href')
                                if product_url and part_number.lower() in product_url.lower():
                                    self.add_comment(f"   🔗 Found product page: {product_url[:50]}...")

                                    # Go to product page
                                    driver.get(product_url)
                                    time.sleep(3)

                                    # Look for STEP download on product page
                                    step_result = self._find_step_on_wurth_product_page(driver, manufacturer, part_number)
                                    if step_result:
                                        return step_result

                            except Exception as e:
                                continue
                    except:
                        continue

                self.add_comment(f"   ❌ No STEP files found on WURTH product pages")
                return None

            finally:
                driver.quit()

        except ImportError:
            self.add_comment(f"   ⚠️ Selenium not available for WURTH search engine interaction")
            return None
        except Exception as e:
            self.add_comment(f"   ❌ WURTH Selenium search error: {str(e)[:50]}")
            return None

    def _find_step_on_wurth_product_page(self, driver, manufacturer, part_number):
        """Find STEP file download on WURTH product page using Selenium"""
        try:
            # Look for STEP download elements on the product page
            step_selectors = [
                'a[href*=".step"]',
                'a[href*=".stp"]',
                'a[title*="STEP"]',
                'a[title*="3D"]',
                '.step-download',
                '.cad-download',
                '.download-step',
                '.btn-step',
                '[data-file-type="step"]',
                'a[href*="download"][href*="3d"]',
                'a[href*="download"][href*="cad"]'
            ]

            for selector in step_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        text = element.text.lower()
                        title = element.get_attribute('title') or ''

                        if href and (
                            any(ext in href.lower() for ext in ['.step', '.stp']) or
                            any(keyword in text for keyword in ['step', '3d', 'model']) or
                            any(keyword in title.lower() for keyword in ['step', '3d', 'model'])
                        ):
                            self.add_comment(f"   📥 Found STEP download: {selector}")

                            # Try to download
                            success = self.download_file_from_url(href, manufacturer, part_number, "3d_model")
                            if success:
                                filename = href.split('/')[-1] if '/' in href else f"{part_number}.step"
                                self.add_comment(f"   ✅ Downloaded STEP file: {filename}")
                                return (href, filename)

                except Exception:
                    continue

            return None

        except Exception as e:
            self.add_comment(f"   ❌ Error finding STEP on product page: {str(e)[:30]}")
            return None

    def learn_wurth_download_pattern(self, part_number, search_url):
        """Specialized learning for WURTH website"""
        try:
            self.add_comment(f"🎓 WURTH Learning Mode for {part_number}")
            self.add_comment(f"🌐 Opening WURTH search page...")

            # Open the WURTH search page
            import webbrowser
            webbrowser.open(search_url)

            # Show WURTH-specific learning dialog
            response = messagebox.askyesnocancel(
                "WURTH Learning Mode",
                f"🎓 WURTH LEARNING MODE\n\n"
                f"Part: {part_number}\n"
                f"Website: WURTH (we-online.com)\n\n"
                f"WURTH SEARCH PROCESS:\n"
                f"1. The search page is now open in your browser\n"
                f"2. You should see search results for {part_number}\n"
                f"3. Click on the product to go to the detail page\n"
                f"4. Look for the STEP file download box/button\n"
                f"5. It may be labeled '3D Model', 'STEP', or 'CAD'\n\n"
                f"Did you find a STEP file download?\n\n"
                f"YES = I found the STEP download box\n"
                f"NO = No STEP file available for this part\n"
                f"CANCEL = Skip this part"
            )

            if response is True:  # YES - Found download
                return self.capture_wurth_download_pattern(part_number, search_url)
            elif response is False:  # NO - No download available
                self.add_comment(f"📝 Learned: WURTH has no STEP file for {part_number}")
                # Save negative result to avoid future searches
                self.save_negative_result("WURTH", part_number)
                return None
            else:  # CANCEL
                self.add_comment(f"⏭️ Skipped WURTH learning for {part_number}")
                return None

        except Exception as e:
            self.add_comment(f"❌ WURTH learning error: {str(e)[:50]}")
            return None

    def capture_wurth_download_pattern(self, part_number, search_url):
        """Capture WURTH-specific download pattern"""
        try:
            # Get the product page URL (where user found the STEP file)
            product_url = simpledialog.askstring(
                "WURTH Product Page URL",
                f"🎯 WURTH STEP FILE CAPTURE\n\n"
                f"Please copy and paste the URL of the product page\n"
                f"where you found the STEP file download:\n\n"
                f"(Copy from browser address bar)\n\n"
                f"Product page URL:",
                initialvalue="https://www.we-online.com/"
            )

            if not product_url or not product_url.startswith('http'):
                self.add_comment("❌ Invalid product page URL")
                return None

            # Get the STEP download URL
            download_url = simpledialog.askstring(
                "WURTH STEP Download URL",
                f"🎯 WURTH STEP DOWNLOAD LINK\n\n"
                f"Now right-click on the STEP file download button/link\n"
                f"and select 'Copy link address'\n\n"
                f"Paste the STEP download URL here:\n\n"
                f"STEP download URL:",
                initialvalue="https://www.we-online.com/"
            )

            if not download_url or not download_url.startswith('http'):
                self.add_comment("❌ Invalid STEP download URL")
                return None

            # Get description of how they found it
            description = simpledialog.askstring(
                "WURTH Download Pattern",
                f"🔍 HOW DID YOU FIND THE STEP FILE?\n\n"
                f"Please describe what you clicked:\n\n"
                f"Examples:\n"
                f"• 'Download 3D Model' button\n"
                f"• 'STEP' link in downloads section\n"
                f"• 'CAD Files' tab then STEP download\n\n"
                f"Description:",
                initialvalue="STEP download button"
            )

            if not description:
                description = "WURTH STEP download"

            # Save the WURTH pattern
            pattern = {
                'part_number': part_number,
                'search_url': search_url,
                'product_url': product_url,
                'download_url': download_url,
                'description': description,
                'site_type': 'wurth_product_page',
                'learned_date': str(datetime.now()),
                'success_count': 0
            }

            if 'wurth' not in self.download_patterns:
                self.download_patterns['wurth'] = []

            self.download_patterns['wurth'].append(pattern)
            self.save_download_patterns()

            self.add_comment(f"🎉 Learned WURTH pattern: {description}")
            self.add_comment(f"🔗 Product page: {product_url[:50]}...")
            self.add_comment(f"📥 Download URL: {download_url[:50]}...")

            # Try to download the file
            success = self.download_file_from_url(download_url, "WURTH", part_number, "3d_model")
            if success:
                pattern['success_count'] = 1
                self.save_download_patterns()
                filename = download_url.split('/')[-1] if '/' in download_url else f"{part_number}.step"
                self.add_comment(f"✅ Successfully downloaded: {filename}")
                return (download_url, filename)
            else:
                self.add_comment("❌ Download failed, but pattern saved for future use")
                return None

        except Exception as e:
            self.add_comment(f"❌ WURTH pattern capture error: {str(e)[:50]}")
            return None

    def save_negative_result(self, site_name, part_number):
        """Save negative results to avoid future searches"""
        try:
            negative_file = os.path.join(os.getcwd(), "negative_results.json")

            # Load existing negative results
            if os.path.exists(negative_file):
                with open(negative_file, 'r') as f:
                    negative_results = json.load(f)
            else:
                negative_results = {}

            # Add new negative result
            site_key = site_name.lower()
            if site_key not in negative_results:
                negative_results[site_key] = []

            negative_results[site_key].append({
                'part_number': part_number,
                'date': str(datetime.now()),
                'reason': 'no_step_file_available'
            })

            # Save negative results
            with open(negative_file, 'w') as f:
                json.dump(negative_results, f, indent=2)

            self.add_comment(f"💾 Saved negative result: {site_name} has no STEP for {part_number}")

        except Exception as e:
            self.add_comment(f"⚠️ Could not save negative result: {str(e)[:30]}")

    def search_ipex_simple(self, manufacturer, part_number):
        """Search for I-PEX/IPEX connectors (try multiple sources)"""
        try:
            self.add_comment(f"🔍 Searching for I-PEX/IPEX connector {part_number}...")
            self.add_comment(f"   💡 Note: I-PEX connectors are made by Hirose Electric")

            # Try distributors first (more reliable for I-PEX parts)
            self.add_comment(f"   🔍 Trying distributors for I-PEX part...")
            distributor_result = self.search_distributors_for_part_info(manufacturer, part_number)
            if distributor_result:
                verified_manufacturer, website, datasheet_url = distributor_result
                self.add_comment(f"   ✅ Found {part_number} on distributors")
                # For I-PEX, distributors often have 3D models too
                return True

            # Try Hirose website as fallback
            search_url = f"https://www.hirose.com/en/products/search?keyword={part_number}"

            import time
            time.sleep(2)  # Be respectful

            response = self.session.get(search_url, timeout=30, allow_redirects=True)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()

                if part_number.lower() in page_text:
                    self.add_comment(f"✅ Found {part_number} on I-PEX")

                    # Look for product image links (I-PEX shows pictures first)
                    product_links = []

                    # Look for product page links
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        link_text = link.get_text().lower()

                        # Make URL absolute
                        if href.startswith('http'):
                            full_url = href
                        elif href.startswith('/'):
                            full_url = urljoin('https://www.hirose.com', href)
                        else:
                            continue

                        # Look for product detail pages
                        if (part_number.lower() in href.lower() or
                            part_number.lower() in link_text or
                            any(keyword in href.lower() for keyword in ['product', 'detail', 'spec'])):
                            product_links.append(full_url)
                            self.add_comment(f"🔗 Found Hirose product page: {full_url[:50]}...")

                    # Try each product page for downloads
                    for product_url in product_links[:3]:  # Try first 3
                        try:
                            time.sleep(2)
                            product_response = self.session.get(product_url, timeout=30)

                            if product_response.status_code == 200:
                                product_soup = BeautifulSoup(product_response.text, 'html.parser')

                                # Look for download links on product page
                                for link in product_soup.find_all('a', href=True):
                                    href = link['href']
                                    link_text = link.get_text().lower()

                                    # Make URL absolute
                                    if href.startswith('http'):
                                        download_url = href
                                    elif href.startswith('/'):
                                        download_url = urljoin('https://www.hirose.com', href)
                                    else:
                                        continue

                                    # Look for STEP files
                                    if (any(ext in download_url.lower() for ext in ['.step', '.stp']) or
                                        any(keyword in link_text for keyword in ['step', '3d', 'cad', 'download'])):

                                        self.add_comment(f"📥 Found Hirose download: {link_text[:30]}...")
                                        success = self.download_file_from_url(download_url, manufacturer, part_number, "3d_model")
                                        if success:
                                            return True

                        except Exception as e:
                            continue

                self.add_comment(f"❌ No STEP files found on Hirose for {part_number}")
            else:
                self.add_comment(f"❌ Hirose search failed: HTTP {response.status_code}")
                self.add_comment(f"   💡 Hirose website may be down - trying alternative sources...")
                # Try alternative sources for I-PEX parts
                return self.search_ipex_alternatives(manufacturer, part_number)

            return False

        except Exception as e:
            self.add_comment(f"❌ Hirose search error: {str(e)[:50]}")
            return False

    def search_ipex_alternatives(self, manufacturer, part_number):
        """Search alternative sources for I-PEX connectors"""
        try:
            self.add_comment(f"🔍 Searching alternative sources for I-PEX {part_number}...")

            # Try common electronics distributors that have 3D models
            alternative_urls = [
                f"https://www.mouser.com/ProductDetail/?qs={part_number}",
                f"https://www.digikey.com/en/products/result?keywords={part_number}",
                f"https://componentsearchengine.com/search?term={part_number}",  # SamacSys
                f"https://www.snapeda.com/search?q={part_number}"  # SnapEDA
            ]

            for search_url in alternative_urls:
                try:
                    import time
                    time.sleep(2)

                    response = self.session.get(search_url, timeout=30)
                    if response.status_code == 200 and part_number.lower() in response.text.lower():
                        site_name = "Mouser" if "mouser" in search_url else "Digi-Key" if "digikey" in search_url else "SamacSys" if "componentsearchengine" in search_url else "SnapEDA"
                        self.add_comment(f"   ✅ Found {part_number} on {site_name}")

                        # Look for 3D model downloads
                        soup = BeautifulSoup(response.text, 'html.parser')
                        for link in soup.find_all('a', href=True):
                            href = link['href']
                            link_text = link.get_text().lower()

                            if (any(ext in href.lower() for ext in ['.step', '.stp']) or
                                any(keyword in link_text for keyword in ['step', '3d', 'cad', 'model'])):

                                # Make URL absolute
                                if href.startswith('http'):
                                    download_url = href
                                elif href.startswith('/'):
                                    base_url = search_url.split('/')[0] + '//' + search_url.split('/')[2]
                                    download_url = urljoin(base_url, href)
                                else:
                                    continue

                                self.add_comment(f"   📥 Found 3D model on {site_name}: {link_text[:30]}...")
                                success = self.download_file_from_url(download_url, manufacturer, part_number, "3d_model")
                                if success:
                                    return True

                except Exception as e:
                    continue

            self.add_comment(f"❌ No I-PEX 3D models found on alternative sources")
            return False

        except Exception as e:
            self.add_comment(f"❌ I-PEX alternative search error: {str(e)[:50]}")
            return False

    def search_diodes_comprehensive(self, manufacturer, part_number):
        """Comprehensive Diodes Inc STEP file search - try ALL methods"""
        try:
            self.add_comment(f"🔍 Comprehensive Diodes Inc search for {part_number}...")
            self.add_comment(f"   💡 We know STEP files exist - trying multiple methods...")

            # Method 1: Try different URL patterns
            url_patterns = [
                f"https://www.diodes.com/search?q={part_number}",
                f"https://www.diodes.com/products/search?q={part_number}",
                f"https://www.diodes.com/part/view/{part_number}",
                f"https://www.diodes.com/products/{part_number}",
                f"https://www.diodes.com/product/{part_number}"
            ]

            import time
            working_url = None
            working_response = None

            # Try each URL pattern until we find the part
            for i, search_url in enumerate(url_patterns):
                self.add_comment(f"   {i+1}. Trying: {search_url[:50]}...")
                try:
                    time.sleep(2)  # Be respectful
                    response = self.session.get(search_url, timeout=30, allow_redirects=True)

                    if response.status_code == 200 and part_number.lower() in response.text.lower():
                        self.add_comment(f"   ✅ Found {part_number} at: {response.url[:50]}...")
                        working_url = response.url
                        working_response = response
                        break
                    else:
                        self.add_comment(f"   ❌ Not found (HTTP {response.status_code})")
                except Exception as e:
                    self.add_comment(f"   ❌ Error: {str(e)[:30]}")
                    continue

            if working_response and working_response.status_code == 200:
                soup = BeautifulSoup(working_response.text, 'html.parser')
                page_text = soup.get_text().lower()

                self.add_comment(f"✅ Analyzing page for STEP files...")

                # Look for package information first
                package_types = ['sot23', 'sot-23', 'soic', 'qfn', 'dfn', 'sop', 'msop', 'tssop', 'ssop']
                detected_package = None

                for package in package_types:
                    if package in page_text:
                        detected_package = package.upper().replace('-', '')
                        self.add_comment(f"📦 Detected package: {detected_package}")
                        break

                # COMPREHENSIVE STEP file detection - try EVERYTHING
                step_candidates = []

                # Method A: Look for ALL possible download elements
                all_elements = (soup.find_all('a', href=True) +
                              soup.find_all('button') +
                              soup.find_all('div', onclick=True) +
                              soup.find_all('span', onclick=True))

                self.add_comment(f"   📊 Scanning {len(all_elements)} elements for downloads...")

                for element in all_elements:
                    href = element.get('href', '')
                    onclick = element.get('onclick', '')
                    text = element.get_text().strip()
                    title = element.get('title', '')
                    class_names = ' '.join(element.get('class', []))
                    data_attrs = ' '.join([f"{k}={v}" for k, v in element.attrs.items() if k.startswith('data-')])

                    # Combine ALL text for analysis
                    combined_text = f"{href} {onclick} {text} {title} {class_names} {data_attrs}".lower()

                    # COMPREHENSIVE STEP indicators
                    step_indicators = [
                        # Direct file extensions
                        '.step', '.stp',
                        # Keywords
                        'step', '3d', 'cad', 'model', 'package', 'mechanical',
                        # Download terms
                        'download', 'file', 'asset', 'attachment',
                        # Package types
                        'sot23', 'sot-23', 'soic', 'qfn', 'dfn', 'sop',
                        # Diodes-specific terms
                        'footprint', 'symbol', 'library'
                    ]

                    # Check if this element has STEP indicators
                    has_step_indicator = any(indicator in combined_text for indicator in step_indicators)

                    if has_step_indicator:
                        # Build full URL
                        full_url = None
                        if href:
                            if href.startswith('http'):
                                full_url = href
                            elif href.startswith('/'):
                                full_url = urljoin('https://www.diodes.com', href)
                            elif href:  # Relative URL
                                full_url = urljoin(working_url, href)

                        if full_url:
                            step_candidates.append({
                                'url': full_url,
                                'text': text,
                                'title': title,
                                'combined': combined_text
                            })
                            self.add_comment(f"   📥 Candidate: {text[:40]} -> {href[:40]}")
                        elif onclick:
                                # Handle JavaScript downloads
                                import re
                                # Look for URLs in onclick
                                url_patterns = [
                                    r'["\']([^"\']*\.st[ep]p?)["\']',  # Direct STEP files
                                    r'["\']([^"\']*download[^"\']*)["\']',  # Download URLs
                                    r'window\.open\(["\']([^"\']+)["\']',  # window.open calls
                                ]

                                for pattern in url_patterns:
                                    matches = re.findall(pattern, onclick, re.IGNORECASE)
                                    for match in matches:
                                        if not match.startswith('http'):
                                            js_url = urljoin('https://www.diodes.com', match)
                                        else:
                                            js_url = match
                                        step_candidates.append({
                                            'url': js_url,
                                            'text': text or 'JS download',
                                            'title': title,
                                            'combined': combined_text
                                        })
                                        self.add_comment(f"   📥 JS Candidate: {text[:40]} -> {match[:40]}")

                    # Test ALL candidates thoroughly
                    self.add_comment(f"   🧪 Testing {len(step_candidates)} candidates...")

                    # Sort by likelihood - direct STEP files first
                    step_candidates.sort(key=lambda x: (
                        '.step' in x['url'].lower() or '.stp' in x['url'].lower(),
                        'download' in x['combined'],
                        'step' in x['combined'],
                        'package' in x['combined']
                    ), reverse=True)

                    for i, candidate in enumerate(step_candidates):
                        self.add_comment(f"   {i+1}. Testing: {candidate['text'][:30]} -> {candidate['url'][:50]}...")

                        # First check if URL is accessible
                        try:
                            time.sleep(1)  # Be respectful
                            test_response = self.session.head(candidate['url'], timeout=15, allow_redirects=True)

                            if test_response.status_code == 200:
                                content_type = test_response.headers.get('content-type', '').lower()
                                content_length = test_response.headers.get('content-length', 'unknown')

                                self.add_comment(f"      ✅ Accessible (Type: {content_type}, Size: {content_length})")

                                # Check if it's likely a STEP file
                                is_likely_step = (
                                    '.step' in candidate['url'].lower() or
                                    '.stp' in candidate['url'].lower() or
                                    'application' in content_type or
                                    'octet-stream' in content_type or
                                    (content_length != 'unknown' and int(content_length or 0) > 1000)
                                )

                                if is_likely_step:
                                    self.add_comment(f"      🎯 Looks like STEP file - attempting download...")
                                    success = self.download_file_from_url(candidate['url'], manufacturer, part_number, "3d_model")
                                    if success:
                                        self.add_comment(f"      🎉 SUCCESS! Downloaded STEP file")
                                        return True
                                    else:
                                        self.add_comment(f"      ❌ Download failed")
                                else:
                                    self.add_comment(f"      ❓ Doesn't look like STEP file")
                            else:
                                self.add_comment(f"      ❌ Not accessible (HTTP {test_response.status_code})")

                        except Exception as e:
                            self.add_comment(f"      ❌ Error testing: {str(e)[:30]}")
                            continue

                    # If no direct STEP files found, try multiple fallback methods
                    if detected_package:
                        self.add_comment(f"🔍 Trying fallback methods for {detected_package} package...")

                        # Try common Diodes Inc STEP file locations
                        package_urls = [
                            # Direct package files
                            f"https://www.diodes.com/assets/Package-Files/{detected_package}.step",
                            f"https://www.diodes.com/assets/Package-Files/{detected_package}.stp",
                            f"https://www.diodes.com/assets/3D-Models/{detected_package}.step",
                            f"https://www.diodes.com/assets/3D-Models/{detected_package}.stp",
                            # Part-specific files
                            f"https://www.diodes.com/assets/Package-Files/{part_number}.step",
                            f"https://www.diodes.com/assets/Package-Files/{part_number}.stp",
                            f"https://www.diodes.com/assets/3D-Models/{part_number}.step",
                            f"https://www.diodes.com/assets/3D-Models/{part_number}.stp",
                            # Alternative paths
                            f"https://www.diodes.com/assets/Datasheets/{detected_package}.step",
                            f"https://www.diodes.com/assets/Datasheets/{detected_package}.stp"
                        ]

                        for package_url in package_urls:
                            try:
                                time.sleep(1)
                                package_response = self.session.get(package_url, timeout=30)
                                if package_response.status_code == 200:
                                    self.add_comment(f"📥 Found {detected_package} package file: {package_url}")
                                    success = self.download_file_from_url(package_url, manufacturer, part_number, "3d_model")
                                    if success:
                                        return True
                            except:
                                continue

                    self.add_comment(f"❌ No STEP files found on Diodes Inc for {part_number}")
                else:
                    self.add_comment(f"❌ Part {part_number} not found on Diodes Inc")
            else:
                self.add_comment(f"❌ Diodes Inc search failed: HTTP {response.status_code}")

            return False

        except Exception as e:
            self.add_comment(f"❌ Diodes Inc search error: {str(e)[:50]}")
            return False

    def search_alternative_step_sources(self, manufacturer, part_number):
        """YOUR COMPREHENSIVE WORKFLOW - Search for STEP files following your 8-step process"""
        try:
            self.add_comment(f"🎯 COMPREHENSIVE WORKFLOW: {manufacturer} {part_number}")
            self.add_comment(f"Following your 8-step systematic approach...")

            # Step 1: We already have manufacturer name and part number

            # Step 2: Validate with Digi-Key/Mouser and get manufacturer website
            website = self.validate_with_distributors(manufacturer, part_number)
            if not website:
                self.add_comment(f"❌ Could not validate part or find manufacturer website")
                return None

            # Step 3: Download datasheet and validate part number + package
            datasheet_result = self.download_and_validate_datasheet(manufacturer, part_number, website)
            if not datasheet_result:
                self.add_comment(f"⚠️ Could not validate datasheet - continuing anyway")

            # Step 4: Website cross-reference is handled automatically

            # Step 5: Search manufacturer website for 3D models
            step_files = self.search_manufacturer_website_for_step(manufacturer, part_number, website)

            if step_files:
                # Step 6: Let user choose which STEP file to save
                chosen_file = self.prompt_user_step_selection(step_files, part_number)
                if chosen_file:
                    self.add_comment(f"✅ User selected STEP file: {chosen_file}")
                    return chosen_file

            # Fallback to alternative sources if manufacturer website fails
            self.add_comment(f"🔄 Manufacturer website failed - trying alternative sources...")

            # Try SnapEDA
            snapeda_result = self.search_snapmagic_enhanced(manufacturer, part_number)
            if snapeda_result:
                return snapeda_result

            # Try SamacSys
            samacsys_result = self.search_samacsys_enhanced(manufacturer, part_number)
            if samacsys_result:
                return samacsys_result

            self.add_comment(f"❌ No STEP files found from any source")
            return None

        except Exception as e:
            self.add_comment(f"❌ Comprehensive workflow error: {str(e)[:50]}")
            return None

    def validate_with_distributors(self, manufacturer, part_number):
        """Step 2: Use Digi-Key/Mouser to validate part and get manufacturer website"""
        self.add_comment(f"📡 Step 2: Validating with distributors...")

        # Try Digi-Key first
        website = self.search_digikey_for_website(manufacturer, part_number)
        if website:
            return website

        # Try Mouser
        website = self.search_mouser_for_website(manufacturer, part_number)
        if website:
            return website

        # Manual entry prompt
        self.add_comment(f"❓ Could not find website automatically")
        return self.prompt_manual_website_entry(manufacturer)

    def search_digikey_for_website(self, manufacturer, part_number):
        """Search Digi-Key for manufacturer website"""
        try:
            url = f"https://www.digikey.com/en/products/result?keywords={part_number}"
            self.add_comment(f"   🔍 Digi-Key: {url[:50]}...")

            response = self.session.get(url, timeout=30)
            if response.status_code == 200 and part_number.lower() in response.text.lower():
                self.add_comment(f"   ✅ Part found on Digi-Key")

                # Extract manufacturer website from page
                soup = BeautifulSoup(response.text, 'html.parser')

                for link in soup.find_all('a', href=True):
                    href = link['href']
                    text = link.get_text().lower()

                    if (manufacturer.lower() in text and
                        href.startswith('http') and
                        'digikey' not in href):

                        website = self.extract_base_url(href)
                        self.add_comment(f"   🌐 Found website: {website}")
                        return website

                self.add_comment(f"   ⚠️ Part found but no manufacturer website")
            else:
                self.add_comment(f"   ❌ Part not found on Digi-Key")

        except Exception as e:
            self.add_comment(f"   ❌ Digi-Key error: {str(e)[:30]}")

        return None

    def search_mouser_for_website(self, manufacturer, part_number):
        """Search Mouser for manufacturer website"""
        try:
            url = f"https://www.mouser.com/ProductDetail/{part_number}"
            self.add_comment(f"   🔍 Mouser: {url[:50]}...")

            response = self.session.get(url, timeout=30)
            if response.status_code == 200 and part_number.lower() in response.text.lower():
                self.add_comment(f"   ✅ Part found on Mouser")

                soup = BeautifulSoup(response.text, 'html.parser')

                for link in soup.find_all('a', href=True):
                    href = link['href']
                    text = link.get_text().lower()

                    if ('datasheet' in text and
                        href.startswith('http') and
                        'mouser' not in href):

                        website = self.extract_base_url(href)
                        self.add_comment(f"   🌐 Found website: {website}")
                        return website

                self.add_comment(f"   ⚠️ Part found but no manufacturer website")
            else:
                self.add_comment(f"   ❌ Part not found on Mouser")

        except Exception as e:
            self.add_comment(f"   ❌ Mouser error: {str(e)[:30]}")

        return None

    def extract_base_url(self, url):
        """Extract base URL from full URL"""
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"

    def prompt_manual_website_entry(self, manufacturer):
        """Prompt user to manually enter manufacturer website"""
        self.add_comment(f"❓ Manual entry required for {manufacturer}")

        # In GUI, this would show a dialog box
        # For now, return None to continue with alternative sources
        return None

    def download_and_validate_datasheet(self, manufacturer, part_number, website):
        """Step 3: Download datasheet and validate part number + extract package"""
        self.add_comment(f"📄 Step 3: Downloading and validating datasheet...")

        # This would implement datasheet finding and validation
        # For now, return a placeholder result
        return {
            'valid': True,
            'package': 'Unknown',
            'datasheet_path': None
        }

    def search_manufacturer_website_for_step(self, manufacturer, part_number, website):
        """Step 5: Search manufacturer website for 3D STEP files"""
        self.add_comment(f"🔍 Step 5: Searching {website} for STEP files...")

        try:
            # Try website search
            search_url = f"{website}/search"
            search_params = {'q': part_number}

            response = self.session.get(search_url, params=search_params, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                step_candidates = []

                # Look for 3D model links
                for element in soup.find_all(['a', 'button']):
                    text = element.get_text().lower()
                    href = element.get('href', '')

                    if any(keyword in f"{text} {href}".lower() for keyword in
                          ['3d', 'step', 'model', 'cad', 'package']):

                        if href:
                            full_url = urljoin(website, href)
                            step_candidates.append({
                                'url': full_url,
                                'description': text.strip()[:50],
                                'type': self.classify_step_type(text, href, part_number)
                            })

                self.add_comment(f"   📥 Found {len(step_candidates)} STEP candidates")
                return step_candidates[:4]  # Return up to 4 candidates

        except Exception as e:
            self.add_comment(f"   ❌ Website search error: {str(e)[:30]}")

        return []

    def classify_step_type(self, text, href, part_number):
        """Classify STEP file type"""
        combined = f"{text} {href}".lower()

        if part_number.lower() in combined:
            return "Part-Specific"
        elif any(pkg in combined for pkg in ['sot23', 'sot-23', '0402', '0603', 'soic']):
            return "Package-Generic"
        else:
            return "Unknown"

    def prompt_user_step_selection(self, step_files, part_number):
        """Step 6: Let user choose which STEP file to save"""
        if not step_files:
            return None

        self.add_comment(f"🎯 Step 6: Found {len(step_files)} STEP files for {part_number}")

        for i, step_file in enumerate(step_files, 1):
            self.add_comment(f"   {i}. {step_file['description']} ({step_file['type']})")

        # In a real GUI, this would show a selection dialog
        # For now, automatically choose the first one
        chosen = step_files[0]
        self.add_comment(f"   ✅ Auto-selected: {chosen['description']}")

        # Download the chosen file
        return self.download_chosen_step_file(chosen, part_number)

    def download_chosen_step_file(self, step_file, part_number):
        """Download the user's chosen STEP file"""
        try:
            response = self.session.get(step_file['url'], timeout=60)

            if response.status_code == 200:
                filename = f"{part_number}_{step_file['type']}.step"
                success = self.download_file_from_url(step_file['url'], "Unknown", part_number, "3d_model")

                if success:
                    return (step_file['url'], filename, "Manufacturer Website")

        except Exception as e:
            self.add_comment(f"   ❌ Download error: {str(e)[:30]}")

        return None

    def search_snapmagic_enhanced(self, manufacturer, part_number):
        """Enhanced SnapMagic/SnapEDA search"""
        try:
            self.add_comment(f"   🔍 SnapMagic/SnapEDA search for {part_number}...")

            # Try both SnapEDA and SnapMagic
            search_urls = [
                f"https://www.snapeda.com/search?q={part_number}",
                f"https://www.snapeda.com/parts/{manufacturer}/{part_number}",
                f"https://www.snapmagic.com/search?q={part_number}"
            ]

            import time
            for i, search_url in enumerate(search_urls):
                site_name = "SnapEDA" if "snapeda" in search_url else "SnapMagic"
                self.add_comment(f"   {i+1}. Trying {site_name}: {search_url[:50]}...")

                try:
                    time.sleep(2)  # Be respectful
                    response = self.session.get(search_url, timeout=30, allow_redirects=True)

                    if response.status_code == 200 and part_number.lower() in response.text.lower():
                        self.add_comment(f"   ✅ Found {part_number} on {site_name}")

                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Look for 3D model/STEP download links
                        step_candidates = []

                        # SnapEDA specific selectors
                        if "snapeda" in search_url:
                            # Look for download buttons and 3D model links
                            for element in soup.find_all(['a', 'button', 'div']):
                                text = element.get_text().lower()
                                href = element.get('href', '')
                                onclick = element.get('onclick', '')

                                if any(keyword in f"{text} {href} {onclick}".lower() for keyword in
                                      ['3d', 'step', 'model', 'cad', 'download', 'mechanical']):

                                    if href:
                                        if href.startswith('http'):
                                            full_url = href
                                        elif href.startswith('/'):
                                            full_url = urljoin('https://www.snapeda.com', href)
                                        else:
                                            continue

                                        step_candidates.append((full_url, text))
                                        self.add_comment(f"      📥 Found: {text[:30]} -> {href[:40]}")

                        # Test candidates
                        for candidate_url, candidate_text in step_candidates[:5]:  # Test first 5
                            self.add_comment(f"      🧪 Testing: {candidate_text[:30]}")

                            try:
                                time.sleep(1)
                                test_response = self.session.head(candidate_url, timeout=15)

                                if test_response.status_code == 200:
                                    content_type = test_response.headers.get('content-type', '').lower()

                                    if (any(ext in candidate_url.lower() for ext in ['.step', '.stp']) or
                                        'application' in content_type or 'octet-stream' in content_type):

                                        self.add_comment(f"      ✅ Found STEP file on {site_name}!")
                                        success = self.download_file_from_url(candidate_url, manufacturer, part_number, "3d_model")
                                        if success:
                                            return (candidate_url, f"{part_number}.step", site_name)

                            except Exception as e:
                                continue
                    else:
                        self.add_comment(f"   ❌ Not found on {site_name} (HTTP {response.status_code})")

                except Exception as e:
                    self.add_comment(f"   ❌ {site_name} error: {str(e)[:30]}")
                    continue

            return None

        except Exception as e:
            self.add_comment(f"   ❌ SnapMagic search error: {str(e)[:50]}")
            return None

    def search_samacsys_enhanced(self, manufacturer, part_number):
        """Enhanced SamacSys search"""
        try:
            self.add_comment(f"   🔍 SamacSys search for {part_number}...")

            # SamacSys Component Search Engine
            search_urls = [
                f"https://componentsearchengine.com/search?term={part_number}",
                f"https://componentsearchengine.com/part-view/{part_number}",
                f"https://www.samacsys.com/library/search?q={part_number}"
            ]

            import time
            for i, search_url in enumerate(search_urls):
                self.add_comment(f"   {i+1}. Trying SamacSys: {search_url[:50]}...")

                try:
                    time.sleep(2)
                    response = self.session.get(search_url, timeout=30, allow_redirects=True)

                    if response.status_code == 200 and part_number.lower() in response.text.lower():
                        self.add_comment(f"   ✅ Found {part_number} on SamacSys")

                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Look for SamacSys download links
                        step_candidates = []

                        for element in soup.find_all(['a', 'button']):
                            text = element.get_text().lower()
                            href = element.get('href', '')

                            # SamacSys specific patterns
                            if any(keyword in f"{text} {href}".lower() for keyword in
                                  ['3d', 'step', 'model', 'cad', 'download', 'library', 'altium', 'kicad']):

                                if href:
                                    if href.startswith('http'):
                                        full_url = href
                                    elif href.startswith('/'):
                                        base_url = 'https://componentsearchengine.com' if 'componentsearchengine' in search_url else 'https://www.samacsys.com'
                                        full_url = urljoin(base_url, href)
                                    else:
                                        continue

                                    step_candidates.append((full_url, text))
                                    self.add_comment(f"      📥 Found: {text[:30]} -> {href[:40]}")

                        # Test SamacSys candidates
                        for candidate_url, candidate_text in step_candidates[:5]:
                            self.add_comment(f"      🧪 Testing: {candidate_text[:30]}")

                            try:
                                time.sleep(1)
                                test_response = self.session.head(candidate_url, timeout=15)

                                if test_response.status_code == 200:
                                    if (any(ext in candidate_url.lower() for ext in ['.step', '.stp']) or
                                        'download' in candidate_url.lower()):

                                        self.add_comment(f"      ✅ Found STEP file on SamacSys!")
                                        success = self.download_file_from_url(candidate_url, manufacturer, part_number, "3d_model")
                                        if success:
                                            return (candidate_url, f"{part_number}.step", "SamacSys")

                            except Exception as e:
                                continue
                    else:
                        self.add_comment(f"   ❌ Not found on SamacSys (HTTP {response.status_code})")

                except Exception as e:
                    self.add_comment(f"   ❌ SamacSys error: {str(e)[:30]}")
                    continue

            return None

        except Exception as e:
            self.add_comment(f"   ❌ SamacSys search error: {str(e)[:50]}")
            return None

    def search_other_component_libraries(self, manufacturer, part_number):
        """Search other component libraries"""
        try:
            self.add_comment(f"   🔍 Other component libraries for {part_number}...")

            # Other sources that might have STEP files
            other_sources = [
                f"https://www.pcblibraries.com/search?q={part_number}",
                f"https://www.3dcontentcentral.com/search.aspx?q={part_number}",
                f"https://grabcad.com/library?q={part_number}",
                f"https://www.traceparts.com/en/search?q={part_number}"
            ]

            import time
            for i, search_url in enumerate(other_sources):
                site_name = search_url.split('/')[2].replace('www.', '')
                self.add_comment(f"   {i+1}. Trying {site_name}: {search_url[:50]}...")

                try:
                    time.sleep(2)
                    response = self.session.get(search_url, timeout=20, allow_redirects=True)

                    if response.status_code == 200 and part_number.lower() in response.text.lower():
                        self.add_comment(f"   ✅ Found {part_number} on {site_name}")

                        # Look for STEP downloads (basic search)
                        soup = BeautifulSoup(response.text, 'html.parser')

                        for link in soup.find_all('a', href=True):
                            href = link['href']
                            text = link.get_text().lower()

                            if (any(ext in href.lower() for ext in ['.step', '.stp']) or
                                any(keyword in text for keyword in ['step', '3d', 'download'])):

                                if href.startswith('/'):
                                    full_url = urljoin(search_url, href)
                                elif href.startswith('http'):
                                    full_url = href
                                else:
                                    continue

                                self.add_comment(f"      📥 Testing: {text[:30]}")
                                success = self.download_file_from_url(full_url, manufacturer, part_number, "3d_model")
                                if success:
                                    return (full_url, f"{part_number}.step", site_name)
                    else:
                        self.add_comment(f"   ❌ Not found on {site_name}")

                except Exception as e:
                    self.add_comment(f"   ❌ {site_name} error: {str(e)[:30]}")
                    continue

            return None

        except Exception as e:
            self.add_comment(f"   ❌ Other libraries error: {str(e)[:50]}")
            return None

    def search_manufacturer_with_learning(self, manufacturer, part_number):
        """Search manufacturer website with manual learning capability"""
        try:
            self.add_comment(f"🔍 Searching {manufacturer} website for {part_number}...")

            # Check if we have a website for this manufacturer
            manufacturer_key = manufacturer.lower().strip()
            website = None

            if manufacturer_key in self.manufacturer_websites:
                website = self.manufacturer_websites[manufacturer_key]
                self.add_comment(f"🌐 Found {manufacturer} website: {website}")
            else:
                # Try to find website by searching
                self.add_comment(f"🔍 No known website for {manufacturer}, entering learning mode...")
                return self.learn_manufacturer_website(manufacturer, part_number)

            # Try to search the manufacturer website
            if website:
                # Try common search patterns
                search_urls = [
                    f"{website}/search?q={part_number}",
                    f"{website}/products?search={part_number}",
                    f"{website}/product-search?keyword={part_number}",
                    f"{website}?s={part_number}"
                ]

                for search_url in search_urls:
                    try:
                        import time
                        time.sleep(2)

                        response = self.session.get(search_url, timeout=30)
                        if response.status_code == 200 and part_number.lower() in response.text.lower():
                            self.add_comment(f"✅ Found {part_number} on {manufacturer} website")

                            # Enter learning mode to let user find the download
                            return self.learn_manufacturer_download(manufacturer, part_number, search_url)

                    except Exception:
                        continue

            # If automatic search failed, enter learning mode
            self.add_comment(f"🎓 Automatic search failed, entering learning mode for {manufacturer}")
            return self.learn_manufacturer_website(manufacturer, part_number)

        except Exception as e:
            self.add_comment(f"❌ Manufacturer search error: {str(e)[:50]}")
            return None

    def learn_manufacturer_website(self, manufacturer, part_number):
        """Learn how to search a manufacturer's website"""
        try:
            # Ask user to find the manufacturer website
            website = simpledialog.askstring(
                f"{manufacturer} Website",
                f"🌐 LEARN {manufacturer.upper()} WEBSITE\n\n"
                f"Please enter the main website URL for {manufacturer}:\n\n"
                f"Example: https://www.{manufacturer.lower().replace(' ', '')}.com\n\n"
                f"Website URL:",
                initialvalue=f"https://www.{manufacturer.lower().replace(' ', '')}.com"
            )

            if not website or not website.startswith('http'):
                self.add_comment(f"❌ Invalid website URL for {manufacturer}")
                return None

            # Save the website
            self.manufacturer_websites[manufacturer.lower().strip()] = website
            self.save_manufacturer_websites()

            self.add_comment(f"💾 Saved {manufacturer} website: {website}")

            # Now try to search for the part
            return self.learn_manufacturer_download(manufacturer, part_number, website)

        except Exception as e:
            self.add_comment(f"❌ Website learning error: {str(e)[:50]}")
            return None

    def learn_manufacturer_download(self, manufacturer, part_number, website_url):
        """Learn how to download from manufacturer website"""
        try:
            self.add_comment(f"🎓 Learning {manufacturer} download process...")

            # Open the website for user
            import webbrowser
            webbrowser.open(website_url)

            # Show learning dialog
            response = messagebox.askyesnocancel(
                f"{manufacturer} Learning Mode",
                f"🎓 {manufacturer.upper()} LEARNING MODE\n\n"
                f"Part: {part_number}\n"
                f"Website: {website_url}\n\n"
                f"SEARCH PROCESS:\n"
                f"1. The website is now open in your browser\n"
                f"2. Search for part number: {part_number}\n"
                f"3. Find the product page\n"
                f"4. Look for STEP file downloads\n\n"
                f"Did you find a STEP file download?\n\n"
                f"YES = I found the STEP download\n"
                f"NO = No STEP file available\n"
                f"CANCEL = Skip this part"
            )

            if response is True:  # YES - Found download
                return self.capture_manufacturer_download_pattern(manufacturer, part_number, website_url)
            elif response is False:  # NO - No download available
                self.add_comment(f"📝 Learned: {manufacturer} has no STEP file for {part_number}")
                self.save_negative_result(manufacturer, part_number)
                return None
            else:  # CANCEL
                self.add_comment(f"⏭️ Skipped {manufacturer} learning for {part_number}")
                return None

        except Exception as e:
            self.add_comment(f"❌ {manufacturer} learning error: {str(e)[:50]}")
            return None

    def capture_manufacturer_download_pattern(self, manufacturer, part_number, website_url):
        """Capture manufacturer-specific download pattern"""
        try:
            # Get the download URL from user
            download_url = simpledialog.askstring(
                f"{manufacturer} Download URL",
                f"🎯 {manufacturer.upper()} STEP FILE CAPTURE\n\n"
                f"Please copy and paste the STEP file download URL:\n\n"
                f"(Right-click on download link → Copy link address)\n\n"
                f"Download URL:",
                initialvalue="https://"
            )

            if not download_url or not download_url.startswith('http'):
                self.add_comment("❌ Invalid download URL")
                return None

            # Get description
            description = simpledialog.askstring(
                f"{manufacturer} Download Pattern",
                f"🔍 HOW DID YOU FIND THE STEP FILE?\n\n"
                f"Please describe the process:\n\n"
                f"Examples:\n"
                f"• Searched → clicked product image → downloads section\n"
                f"• Product page → CAD files tab → STEP download\n"
                f"• Downloads page → 3D models → STEP file\n\n"
                f"Description:",
                initialvalue=f"{manufacturer} STEP download process"
            )

            # Save the pattern
            pattern = {
                'manufacturer': manufacturer,
                'part_number': part_number,
                'website_url': website_url,
                'download_url': download_url,
                'description': description or f"{manufacturer} download",
                'learned_date': str(datetime.now()),
                'success_count': 0
            }

            manufacturer_key = manufacturer.lower().replace(' ', '_')
            if manufacturer_key not in self.download_patterns:
                self.download_patterns[manufacturer_key] = []

            self.download_patterns[manufacturer_key].append(pattern)
            self.save_download_patterns()

            self.add_comment(f"🎉 Learned {manufacturer} pattern: {description}")

            # Try to download the file
            success = self.download_file_from_url(download_url, manufacturer, part_number, "3d_model")
            if success:
                pattern['success_count'] = 1
                self.save_download_patterns()
                return (download_url, f"{part_number}.step", manufacturer)
            else:
                self.add_comment("❌ Download failed, but pattern saved")
                return None

        except Exception as e:
            self.add_comment(f"❌ Pattern capture error: {str(e)[:50]}")
            return None

    def save_manufacturer_websites(self):
        """Save manufacturer websites to CSV file"""
        try:
            csv_file = os.path.join(os.getcwd(), "actual-web-site-xref.csv")

            # Read existing CSV
            existing_data = {}
            if os.path.exists(csv_file):
                with open(csv_file, 'r', newline='', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    for row in reader:
                        if len(row) >= 2:
                            existing_data[row[0].lower()] = row[1]

            # Add new websites
            for manufacturer, website in self.manufacturer_websites.items():
                existing_data[manufacturer] = website

            # Write back to CSV
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for manufacturer, website in existing_data.items():
                    writer.writerow([manufacturer, website])

            self.add_comment(f"💾 Updated manufacturer websites CSV")

        except Exception as e:
            self.add_comment(f"⚠️ Could not save websites: {str(e)[:30]}")

    def search_ultralibrarian_with_learning(self, manufacturer, part_number):
        """UltraLibrarian search with specialized learning"""
        try:
            self.add_comment(f"   🔍 UltraLibrarian specialized search for {part_number}...")

            search_url = f"https://www.ultralibrarian.com/search?q={part_number}"

            # Try learned patterns first
            learned_result = self.apply_learned_patterns("UltraLibrarian", part_number, search_url)
            if learned_result:
                return learned_result

            # Try automatic search
            auto_result = self.search_ultralibrarian(manufacturer, part_number)
            if auto_result:
                return auto_result

            # Check if part exists but download failed
            if self._part_found_on_site(search_url, part_number):
                self.add_comment(f"   🎓 UltraLibrarian: Part found but download failed - entering learning mode")
                return self.learn_download_pattern("UltraLibrarian", part_number, search_url)

            return None

        except Exception as e:
            self.add_comment(f"   ❌ UltraLibrarian specialized search error: {str(e)[:50]}")
            return None

    def search_snapmagic_with_learning(self, manufacturer, part_number):
        """SnapMagic search with specialized learning"""
        try:
            self.add_comment(f"   🔍 SnapMagic specialized search for {part_number}...")

            # Try both SnapMagic and SnapEDA
            search_urls = [
                f"https://www.snapeda.com/search?q={part_number}",
                f"https://www.snapmagic.com/search?q={part_number}"
            ]

            for search_url in search_urls:
                site_name = "SnapEDA" if "snapeda" in search_url else "SnapMagic"

                # Try learned patterns first
                learned_result = self.apply_learned_patterns(site_name, part_number, search_url)
                if learned_result:
                    return learned_result

                # Try automatic search
                auto_result = self.search_snapmagic(manufacturer, part_number)
                if auto_result:
                    return auto_result

                # Check if part exists but download failed
                if self._part_found_on_site(search_url, part_number):
                    self.add_comment(f"   🎓 {site_name}: Part found but download failed - entering learning mode")
                    return self.learn_download_pattern(site_name, part_number, search_url)

            return None

        except Exception as e:
            self.add_comment(f"   ❌ SnapMagic specialized search error: {str(e)[:50]}")
            return None

    def search_samacsys_with_learning(self, manufacturer, part_number):
        """SamacSys search with specialized learning"""
        try:
            self.add_comment(f"   🔍 SamacSys specialized search for {part_number}...")

            search_url = f"https://componentsearchengine.com/search?term={part_number}"

            # Try learned patterns first
            learned_result = self.apply_learned_patterns("SamacSys", part_number, search_url)
            if learned_result:
                return learned_result

            # Try automatic search
            auto_result = self.search_samacsys(manufacturer, part_number)
            if auto_result:
                return auto_result

            # Check if part exists but download failed
            if self._part_found_on_site(search_url, part_number):
                self.add_comment(f"   🎓 SamacSys: Part found but download failed - entering learning mode")
                return self.learn_download_pattern("SamacSys", part_number, search_url)

            return None

        except Exception as e:
            self.add_comment(f"   ❌ SamacSys specialized search error: {str(e)[:50]}")
            return None

    def _part_found_on_site(self, search_url, part_number):
        """Generic method to check if part exists on any website"""
        try:
            import time
            time.sleep(2)  # Be respectful

            response = self.session.get(search_url, timeout=30)
            if response.status_code == 200:
                page_text = response.text.lower()
                return (part_number.lower() in page_text or
                       any(keyword in page_text for keyword in ['product', 'component', 'part', 'model']))
            return False
        except:
            return False

    def search_google_for_datasheet(self, manufacturer, part_number):
        """Search Google for datasheet PDF"""
        try:
            self.add_comment(f"   🔍 Google search: {manufacturer} {part_number} datasheet")

            # Create Google search URL for datasheet PDF
            import urllib.parse
            query = f"{manufacturer} {part_number} datasheet filetype:pdf"
            encoded_query = urllib.parse.quote(query)
            google_url = f"https://www.google.com/search?q={encoded_query}"

            import time
            time.sleep(2)  # Be respectful to Google

            # Use a more realistic user agent for Google
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = self.session.get(google_url, headers=headers, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for PDF links in Google search results
                for link in soup.find_all('a', href=True):
                    href = link['href']

                    # Google search results have URLs in format: /url?q=ACTUAL_URL&...
                    if '/url?q=' in href:
                        # Extract the actual URL
                        import urllib.parse
                        parsed = urllib.parse.parse_qs(urllib.parse.urlparse(href).query)
                        if 'q' in parsed:
                            actual_url = parsed['q'][0]

                            # Check if it's a PDF
                            if actual_url.lower().endswith('.pdf') or 'pdf' in actual_url.lower():
                                self.add_comment(f"   📄 Found PDF link: {actual_url[:50]}...")

                                # Try to download the PDF
                                success = self.download_file_from_url(actual_url, manufacturer, part_number, "datasheet")
                                if success:
                                    return True

                # Also look for direct PDF links (not through Google redirect)
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    if href.startswith('http') and href.lower().endswith('.pdf'):
                        # Check if it contains the part number
                        if part_number.lower() in href.lower() or part_number.lower() in link.get_text().lower():
                            self.add_comment(f"   📄 Found direct PDF: {href[:50]}...")
                            success = self.download_file_from_url(href, manufacturer, part_number, "datasheet")
                            if success:
                                return True

                self.add_comment(f"   ❌ No PDF datasheets found in Google results")
                return False
            else:
                self.add_comment(f"   ❌ Google search failed: HTTP {response.status_code}")
                return False

        except Exception as e:
            self.add_comment(f"   ❌ Google search error: {str(e)[:50]}")
            return False

    def learn_datasheet_pattern(self, manufacturer, part_number):
        """Learn datasheet download patterns"""
        try:
            self.add_comment(f"🎓 Learning datasheet pattern for {manufacturer} {part_number}")

            # Try common datasheet sources
            search_urls = [
                f"https://www.digikey.com/en/products/result?keywords={part_number}",
                f"https://www.mouser.com/ProductDetail/?qs={part_number}",
                f"https://www.google.com/search?q={manufacturer}+{part_number}+datasheet+filetype:pdf"
            ]

            for search_url in search_urls:
                site_name = "Digi-Key" if "digikey" in search_url else "Mouser" if "mouser" in search_url else "Google"

                if self._part_found_on_site(search_url, part_number):
                    self.add_comment(f"🎓 Found {part_number} on {site_name} - entering datasheet learning mode")
                    return self.learn_download_pattern(f"{site_name}_Datasheet", part_number, search_url)

            return None

        except Exception as e:
            self.add_comment(f"❌ Datasheet learning error: {str(e)[:50]}")
            return None

    def search_ultralibrarian(self, manufacturer, part_number):
        """Search UltraLibrarian for STEP files with enhanced scraping and learning"""
        try:
            self.add_comment(f"   🔍 Searching UltraLibrarian for {part_number}...")

            # First, try learned patterns
            search_url = f"https://www.ultralibrarian.com/search?q={part_number}"
            learned_result = self.apply_learned_patterns("UltraLibrarian", part_number, search_url)
            if learned_result:
                return learned_result

            # Try Selenium search
            selenium_result = self._search_ultralibrarian_selenium(manufacturer, part_number)
            if selenium_result:
                return selenium_result

            # Try requests-based search
            requests_result = self._search_ultralibrarian_requests(manufacturer, part_number)
            if requests_result:
                return requests_result

            # If part found but no download worked, enter learning mode
            if self.learning_mode and self._part_found_on_ultralibrarian(part_number):
                self.add_comment(f"   🎓 Part found but download failed - entering learning mode")
                return self.learn_download_pattern("UltraLibrarian", part_number, search_url)

            return None

        except Exception as e:
            self.add_comment(f"   ❌ UltraLibrarian error: {str(e)[:50]}")
            return None

    def _part_found_on_ultralibrarian(self, part_number):
        """Quick check if part exists on UltraLibrarian"""
        try:
            search_url = f"https://www.ultralibrarian.com/search?q={part_number}"
            response = self.session.get(search_url, timeout=15)
            return part_number.lower() in response.text.lower()
        except:
            return False

    def _search_ultralibrarian_selenium(self, manufacturer, part_number):
        """Search UltraLibrarian using Selenium for JavaScript support"""
        try:
            # Try to import Selenium
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            from selenium.common.exceptions import TimeoutException, WebDriverException

            # Setup Chrome options
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # Run in background
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            driver = webdriver.Chrome(options=chrome_options)

            try:
                search_url = f"https://www.ultralibrarian.com/search?q={part_number}"
                self.add_comment(f"   🌐 Loading UltraLibrarian with Selenium...")

                driver.get(search_url)

                # Wait for search results to load
                wait = WebDriverWait(driver, 10)

                # Look for search results
                try:
                    # Wait for results container
                    wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".search-results, .component-list, .parts-list")))

                    # Check if part number appears in results
                    page_source = driver.page_source.lower()
                    if part_number.lower() not in page_source:
                        self.add_comment(f"   ❌ {part_number} not found in UltraLibrarian results")
                        return None

                    self.add_comment(f"   ✅ Found {part_number} on UltraLibrarian")

                    # Enhanced selectors for UltraLibrarian download links
                    download_selectors = [
                        'a[href*="download"]',
                        'a[href*=".step"]',
                        'a[href*=".stp"]',
                        '.download-btn',
                        '.cad-download',
                        '.model-download',
                        'a[title*="3D"]',
                        'a[title*="STEP"]',
                        'a[title*="Model"]',
                        '.btn-download',
                        '[data-download-type="3d"]',
                        '[data-file-type="step"]'
                    ]

                    # Try each selector
                    for selector in download_selectors:
                        try:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                href = element.get_attribute('href')
                                text = element.text.lower()
                                title = element.get_attribute('title') or ''

                                if href and (
                                    any(keyword in text for keyword in ['step', '3d', 'model', 'download']) or
                                    any(keyword in title.lower() for keyword in ['step', '3d', 'model']) or
                                    any(ext in href.lower() for ext in ['.step', '.stp']) or
                                    'download' in href.lower()
                                ):
                                    self.add_comment(f"   📥 Found download link: {href[:50]}...")

                                    # Try to download
                                    success = self.download_file_from_url(href, manufacturer, part_number, "3d_model")
                                    if success:
                                        filename = f"{part_number}_UltraLibrarian.step"
                                        return (href, filename, "UltraLibrarian")
                        except Exception as e:
                            continue

                    self.add_comment(f"   ❌ No downloadable STEP files found on UltraLibrarian")
                    return None

                except TimeoutException:
                    self.add_comment(f"   ⏱️ UltraLibrarian page load timeout")
                    return None

            finally:
                driver.quit()

        except ImportError:
            self.add_comment(f"   ⚠️ Selenium not available, falling back to requests")
            return None
        except WebDriverException as e:
            self.add_comment(f"   ⚠️ Selenium WebDriver error: {str(e)[:30]}")
            return None
        except Exception as e:
            self.add_comment(f"   ❌ Selenium error: {str(e)[:30]}")
            return None

    def _search_ultralibrarian_requests(self, manufacturer, part_number):
        """Fallback search using requests library"""
        try:
            search_url = f"https://www.ultralibrarian.com/search?q={part_number}"

            import time
            time.sleep(2)  # Be respectful

            response = self.session.get(search_url, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()

                if part_number.lower() in page_text:
                    self.add_comment(f"   ✅ Found {part_number} on UltraLibrarian (requests)")

                    # Enhanced link detection
                    download_patterns = [
                        {'tag': 'a', 'attrs': {'href': True}, 'text_contains': ['step', '3d', 'model', 'download']},
                        {'tag': 'a', 'attrs': {'class': ['download', 'btn-download', 'cad-download']}, 'href': True},
                        {'tag': 'button', 'attrs': {'data-download': True}, 'onclick': True}
                    ]

                    for pattern in download_patterns:
                        elements = soup.find_all(pattern['tag'], pattern.get('attrs', {}))
                        for element in elements:
                            href = element.get('href') or element.get('onclick', '')
                            text = element.get_text().lower()

                            if href and (
                                any(keyword in text for keyword in pattern.get('text_contains', [])) or
                                any(ext in href.lower() for ext in ['.step', '.stp']) or
                                'download' in href.lower()
                            ):
                                # Make URL absolute
                                if href.startswith('http'):
                                    full_url = href
                                elif href.startswith('/'):
                                    full_url = urljoin('https://www.ultralibrarian.com', href)
                                else:
                                    continue

                                # Try to download
                                success = self.download_file_from_url(full_url, manufacturer, part_number, "3d_model")
                                if success:
                                    filename = f"{part_number}_UltraLibrarian.step"
                                    return (full_url, filename, "UltraLibrarian")

                self.add_comment(f"   ❌ No STEP files found on UltraLibrarian (requests)")
            else:
                self.add_comment(f"   ❌ UltraLibrarian HTTP {response.status_code}")

            return None

        except Exception as e:
            self.add_comment(f"   ❌ UltraLibrarian requests error: {str(e)[:30]}")
            return None

    def search_snapmagic(self, manufacturer, part_number):
        """Search SnapMagic for STEP files with enhanced scraping"""
        try:
            self.add_comment(f"   🔍 Searching SnapMagic for {part_number}...")

            # Try Selenium first for better JavaScript support
            selenium_result = self._search_snapmagic_selenium(manufacturer, part_number)
            if selenium_result:
                return selenium_result

            # Fallback to requests-based search
            return self._search_snapmagic_requests(manufacturer, part_number)

        except Exception as e:
            self.add_comment(f"   ❌ SnapMagic error: {str(e)[:50]}")
            return None

    def _search_snapmagic_selenium(self, manufacturer, part_number):
        """Search SnapMagic using Selenium"""
        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            from selenium.common.exceptions import TimeoutException, WebDriverException

            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            driver = webdriver.Chrome(options=chrome_options)

            try:
                # Try both SnapMagic and SnapEDA URLs
                search_urls = [
                    f"https://www.snapeda.com/search?q={part_number}",
                    f"https://www.snapmagic.com/search?q={part_number}"
                ]

                for search_url in search_urls:
                    try:
                        self.add_comment(f"   🌐 Loading {search_url.split('/')[2]} with Selenium...")
                        driver.get(search_url)

                        wait = WebDriverWait(driver, 10)

                        # Wait for search results
                        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".search-results, .part-list, .component-grid")))

                        # Check if part found
                        page_source = driver.page_source.lower()
                        if part_number.lower() not in page_source:
                            continue

                        self.add_comment(f"   ✅ Found {part_number} on {search_url.split('/')[2]}")

                        # SnapEDA/SnapMagic specific selectors
                        download_selectors = [
                            'a[href*="download"]',
                            'a[href*=".step"]',
                            'a[href*=".stp"]',
                            '.download-button',
                            '.cad-download',
                            '.model-download',
                            '.btn-3d',
                            '.step-download',
                            'a[title*="3D Model"]',
                            'a[title*="STEP"]',
                            '.download-cad',
                            '[data-format="step"]',
                            '[data-type="3d"]'
                        ]

                        for selector in download_selectors:
                            try:
                                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                                for element in elements:
                                    href = element.get_attribute('href')
                                    text = element.text.lower()
                                    title = element.get_attribute('title') or ''

                                    if href and (
                                        any(keyword in text for keyword in ['step', '3d', 'model', 'download']) or
                                        any(keyword in title.lower() for keyword in ['step', '3d', 'model']) or
                                        any(ext in href.lower() for ext in ['.step', '.stp'])
                                    ):
                                        self.add_comment(f"   📥 Found download link: {href[:50]}...")

                                        success = self.download_file_from_url(href, manufacturer, part_number, "3d_model")
                                        if success:
                                            filename = f"{part_number}_SnapMagic.step"
                                            return (href, filename, "SnapMagic")
                            except Exception:
                                continue

                    except TimeoutException:
                        continue
                    except Exception:
                        continue

                self.add_comment(f"   ❌ No STEP files found on SnapMagic/SnapEDA")
                return None

            finally:
                driver.quit()

        except ImportError:
            return None
        except Exception as e:
            self.add_comment(f"   ❌ SnapMagic Selenium error: {str(e)[:30]}")
            return None

    def _search_snapmagic_requests(self, manufacturer, part_number):
        """Fallback SnapMagic search using requests"""
        try:
            import time

            # Try both SnapEDA and SnapMagic
            search_urls = [
                f"https://www.snapeda.com/search?q={part_number}",
                f"https://www.snapmagic.com/search?q={part_number}"
            ]

            for search_url in search_urls:
                try:
                    time.sleep(2)
                    response = self.session.get(search_url, timeout=30)

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        page_text = soup.get_text().lower()

                        if part_number.lower() in page_text:
                            site_name = search_url.split('/')[2]
                            self.add_comment(f"   ✅ Found {part_number} on {site_name} (requests)")

                            # Enhanced selectors for SnapEDA/SnapMagic
                            selectors = [
                                'a[href*="download"]',
                                'a[href*=".step"]',
                                'a[href*=".stp"]',
                                '.download-btn',
                                '.cad-download',
                                '.model-download'
                            ]

                            for selector in selectors:
                                elements = soup.select(selector)
                                for element in elements:
                                    href = element.get('href')
                                    text = element.get_text().lower()

                                    if href and (
                                        any(keyword in text for keyword in ['step', '3d', 'model']) or
                                        any(ext in href.lower() for ext in ['.step', '.stp'])
                                    ):
                                        # Make URL absolute
                                        if href.startswith('http'):
                                            full_url = href
                                        elif href.startswith('/'):
                                            full_url = urljoin(f"https://{site_name}", href)
                                        else:
                                            continue

                                        success = self.download_file_from_url(full_url, manufacturer, part_number, "3d_model")
                                        if success:
                                            filename = f"{part_number}_SnapMagic.step"
                                            return (full_url, filename, "SnapMagic")

                except Exception:
                    continue

            self.add_comment(f"   ❌ No STEP files found on SnapMagic/SnapEDA (requests)")
            return None

        except Exception as e:
            self.add_comment(f"   ❌ SnapMagic requests error: {str(e)[:30]}")
            return None

    def search_samacsys(self, manufacturer, part_number):
        """Search SamacSys for STEP files with enhanced scraping"""
        try:
            self.add_comment(f"   🔍 Searching SamacSys for {part_number}...")

            # Try Selenium first for better JavaScript support
            selenium_result = self._search_samacsys_selenium(manufacturer, part_number)
            if selenium_result:
                return selenium_result

            # Fallback to requests-based search
            return self._search_samacsys_requests(manufacturer, part_number)

        except Exception as e:
            self.add_comment(f"   ❌ SamacSys error: {str(e)[:50]}")
            return None

    def _search_samacsys_selenium(self, manufacturer, part_number):
        """Search SamacSys using Selenium"""
        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            from selenium.common.exceptions import TimeoutException, WebDriverException

            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            driver = webdriver.Chrome(options=chrome_options)

            try:
                search_url = f"https://componentsearchengine.com/search?term={part_number}"
                self.add_comment(f"   🌐 Loading SamacSys with Selenium...")

                driver.get(search_url)
                wait = WebDriverWait(driver, 15)  # SamacSys can be slower

                # Wait for search results
                try:
                    wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".search-results, .component-list, .part-results")))

                    # Check if part found
                    page_source = driver.page_source.lower()
                    if part_number.lower() not in page_source:
                        self.add_comment(f"   ❌ {part_number} not found in SamacSys results")
                        return None

                    self.add_comment(f"   ✅ Found {part_number} on SamacSys")

                    # SamacSys specific selectors
                    download_selectors = [
                        'a[href*="download"]',
                        'a[href*=".step"]',
                        'a[href*=".stp"]',
                        '.download-link',
                        '.cad-download',
                        '.library-download',
                        '.model-download',
                        '.btn-download',
                        'a[title*="3D"]',
                        'a[title*="STEP"]',
                        'a[title*="Model"]',
                        '[data-format="step"]',
                        '[data-type="3d-model"]',
                        '.download-cad-model'
                    ]

                    for selector in download_selectors:
                        try:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                href = element.get_attribute('href')
                                text = element.text.lower()
                                title = element.get_attribute('title') or ''

                                if href and (
                                    any(keyword in text for keyword in ['step', '3d', 'model', 'download', 'cad']) or
                                    any(keyword in title.lower() for keyword in ['step', '3d', 'model', 'cad']) or
                                    any(ext in href.lower() for ext in ['.step', '.stp']) or
                                    'download' in href.lower()
                                ):
                                    self.add_comment(f"   📥 Found download link: {href[:50]}...")

                                    success = self.download_file_from_url(href, manufacturer, part_number, "3d_model")
                                    if success:
                                        filename = f"{part_number}_SamacSys.step"
                                        return (href, filename, "SamacSys")
                        except Exception:
                            continue

                    self.add_comment(f"   ❌ No downloadable STEP files found on SamacSys")
                    return None

                except TimeoutException:
                    self.add_comment(f"   ⏱️ SamacSys page load timeout")
                    return None

            finally:
                driver.quit()

        except ImportError:
            return None
        except Exception as e:
            self.add_comment(f"   ❌ SamacSys Selenium error: {str(e)[:30]}")
            return None

    def _search_samacsys_requests(self, manufacturer, part_number):
        """Fallback SamacSys search using requests"""
        try:
            search_url = f"https://componentsearchengine.com/search?term={part_number}"

            import time
            time.sleep(3)  # SamacSys needs more time

            response = self.session.get(search_url, timeout=45)  # Longer timeout

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()

                if part_number.lower() in page_text:
                    self.add_comment(f"   ✅ Found {part_number} on SamacSys (requests)")

                    # Enhanced selectors for SamacSys
                    selectors = [
                        'a[href*="download"]',
                        'a[href*=".step"]',
                        'a[href*=".stp"]',
                        '.download-link',
                        '.cad-download',
                        '.library-download',
                        '.model-download'
                    ]

                    for selector in selectors:
                        elements = soup.select(selector)
                        for element in elements:
                            href = element.get('href')
                            text = element.get_text().lower()

                            if href and (
                                any(keyword in text for keyword in ['step', '3d', 'model', 'download', 'cad']) or
                                any(ext in href.lower() for ext in ['.step', '.stp']) or
                                'download' in href.lower()
                            ):
                                # Make URL absolute
                                if href.startswith('http'):
                                    full_url = href
                                elif href.startswith('/'):
                                    full_url = urljoin('https://componentsearchengine.com', href)
                                else:
                                    continue

                                success = self.download_file_from_url(full_url, manufacturer, part_number, "3d_model")
                                if success:
                                    filename = f"{part_number}_SamacSys.step"
                                    return (full_url, filename, "SamacSys")

                self.add_comment(f"   ❌ No STEP files found on SamacSys (requests)")
            else:
                self.add_comment(f"   ❌ SamacSys HTTP {response.status_code}")

            return None

        except Exception as e:
            self.add_comment(f"   ❌ SamacSys requests error: {str(e)[:30]}")
            return None

    def ask_for_help_finding_file(self, manufacturer, part_number, file_type):
        """Ask user for help when file cannot be found automatically"""
        try:
            self.add_comment(f"🤔 Need help finding {file_type} for {manufacturer} {part_number}")

            # Show dialog asking for help
            response = messagebox.askyesnocancel(
                f"Help Needed - {file_type.title()} Not Found",
                f"Could not automatically find {file_type} for:\n\n"
                f"Manufacturer: {manufacturer}\n"
                f"Part Number: {part_number}\n\n"
                f"Options:\n"
                f"YES = Stop processing and let me help find it manually\n"
                f"NO = Skip this part and continue with next parts\n"
                f"CANCEL = Stop all processing"
            )

            if response is True:  # YES - Stop for manual help
                self.add_comment(f"⏸️ Stopping for manual help with {manufacturer} {part_number}")
                self.add_comment(f"💡 Please help find the {file_type} manually:")
                self.add_comment(f"   1. Search for {manufacturer} {part_number} manually")
                self.add_comment(f"   2. Find the {file_type} file")
                self.add_comment(f"   3. Update the program with new search patterns")
                self.add_comment(f"   4. Resume processing")

                # Show additional help dialog
                messagebox.showinfo(
                    "Manual Help Required",
                    f"Processing stopped for manual intervention.\n\n"
                    f"Please:\n"
                    f"1. Manually search for {manufacturer} {part_number}\n"
                    f"2. Find the {file_type} file\n"
                    f"3. Note the website/search method that worked\n"
                    f"4. Update the program code with new patterns\n"
                    f"5. Resume processing\n\n"
                    f"This helps the program learn new search methods!"
                )

                return True  # Stop processing

            elif response is False:  # NO - Skip this part
                self.add_comment(f"⏭️ Skipping {manufacturer} {part_number} - continuing with next parts")
                return False  # Continue processing

            else:  # CANCEL - Stop all processing
                self.add_comment(f"🛑 User cancelled all processing")
                # Set a flag to stop all processing
                self.stop_all_processing = True
                return True  # Stop processing

        except Exception as e:
            self.add_comment(f"❌ Error in help dialog: {str(e)}")
            return False

    def show_learning_suggestions(self, manufacturer, part_number, file_type):
        """Show suggestions for learning new search patterns"""
        suggestions = []

        if file_type == "datasheet":
            suggestions = [
                f"Try searching '{manufacturer} {part_number} datasheet' on Google",
                f"Check {manufacturer}'s official website",
                f"Look for product pages or documentation sections",
                f"Check if part number has variations (with/without hyphens, etc.)"
            ]
        elif file_type == "step":
            suggestions = [
                f"Try searching '{manufacturer} {part_number} 3D model' or 'STEP file'",
                f"Check {manufacturer}'s CAD library or 3D model section",
                f"Look for 'Downloads', 'CAD Models', or '3D Files' sections",
                f"Some manufacturers have separate CAD portals"
            ]

        suggestion_text = "\n".join([f"• {s}" for s in suggestions])

        messagebox.showinfo(
            f"Search Suggestions - {file_type.title()}",
            f"Suggestions for finding {file_type} for {manufacturer} {part_number}:\n\n"
            f"{suggestion_text}\n\n"
            f"Once you find a working method, we can add it to the program!"
        )

    def load_download_patterns(self):
        """Load saved download patterns from JSON file"""
        try:
            patterns_file = os.path.join(os.getcwd(), "download_patterns.json")
            if os.path.exists(patterns_file):
                with open(patterns_file, 'r') as f:
                    patterns = json.load(f)
                self.add_comment(f"📚 Loaded {len(patterns)} download patterns")
                return patterns
            else:
                self.add_comment("📚 No existing download patterns found - will learn new ones")
                return {}
        except Exception as e:
            self.add_comment(f"⚠️ Error loading download patterns: {str(e)[:50]}")
            return {}

    def save_download_patterns(self):
        """Save download patterns to JSON file"""
        try:
            patterns_file = os.path.join(os.getcwd(), "download_patterns.json")
            with open(patterns_file, 'w') as f:
                json.dump(self.download_patterns, f, indent=2)
            self.add_comment(f"💾 Saved {len(self.download_patterns)} download patterns")
        except Exception as e:
            self.add_comment(f"❌ Error saving download patterns: {str(e)[:50]}")

    def learn_download_pattern(self, site_name, part_number, search_url):
        """Interactive learning - show webpage and let user find download link"""
        try:
            self.add_comment(f"🎓 Learning mode: Found {part_number} on {site_name}")
            self.add_comment(f"🌐 Opening webpage for manual inspection...")

            # Open the webpage in browser for user to see
            import webbrowser
            webbrowser.open(search_url)

            # Show learning dialog
            response = messagebox.askyesnocancel(
                f"Learning Mode - {site_name}",
                f"🎓 LEARNING MODE ACTIVATED\n\n"
                f"Part found: {part_number}\n"
                f"Website: {site_name}\n\n"
                f"The webpage is now open in your browser.\n"
                f"Please look for the STEP file download link.\n\n"
                f"Did you find a STEP file download?\n\n"
                f"YES = I found it, let me teach the system\n"
                f"NO = No STEP file available on this page\n"
                f"CANCEL = Skip this part"
            )

            if response is True:  # YES - Found download
                return self.capture_download_pattern(site_name, part_number, search_url)
            elif response is False:  # NO - No download available
                self.add_comment(f"📝 Learned: {site_name} has no STEP file for {part_number}")
                return None
            else:  # CANCEL
                self.add_comment(f"⏭️ Skipped learning for {part_number}")
                return None

        except Exception as e:
            self.add_comment(f"❌ Learning error: {str(e)[:50]}")
            return None

    def capture_download_pattern(self, site_name, part_number, search_url):
        """Capture the download pattern from user input"""
        try:
            # Get download URL from user
            download_url = simpledialog.askstring(
                "Capture Download Link",
                f"🎯 CAPTURE DOWNLOAD PATTERN\n\n"
                f"Please copy and paste the STEP file download URL:\n\n"
                f"(Right-click on download link → Copy link address)\n\n"
                f"Download URL:",
                initialvalue="https://"
            )

            if not download_url or not download_url.startswith('http'):
                self.add_comment("❌ Invalid download URL provided")
                return None

            # Get CSS selector or pattern description
            pattern_info = simpledialog.askstring(
                "Capture Download Pattern",
                f"🔍 CAPTURE PATTERN DETAILS\n\n"
                f"How did you find this download link?\n"
                f"Please describe the pattern:\n\n"
                f"Examples:\n"
                f"• 'Download STEP' button\n"
                f"• Link with text '3D Model'\n"
                f"• Button with class 'download-btn'\n\n"
                f"Pattern description:",
                initialvalue="Download button"
            )

            if not pattern_info:
                pattern_info = "Manual download link"

            # Try to extract pattern from the URL
            url_pattern = self.extract_url_pattern(download_url, search_url)

            # Save the pattern
            site_key = site_name.lower().replace(' ', '_')
            if site_key not in self.download_patterns:
                self.download_patterns[site_key] = []

            pattern = {
                'part_number': part_number,
                'search_url': search_url,
                'download_url': download_url,
                'url_pattern': url_pattern,
                'description': pattern_info,
                'learned_date': str(datetime.now()),
                'success_count': 0
            }

            self.download_patterns[site_key].append(pattern)
            self.save_download_patterns()

            self.add_comment(f"🎉 Learned new pattern for {site_name}!")
            self.add_comment(f"📝 Pattern: {pattern_info}")
            self.add_comment(f"🔗 URL pattern: {url_pattern}")

            # Try to download the file
            success = self.download_file_from_url(download_url, "Unknown", part_number, "3d_model")
            if success:
                pattern['success_count'] = 1
                self.save_download_patterns()
                filename = f"{part_number}_{site_name}.step"
                return (download_url, filename, site_name)
            else:
                self.add_comment("❌ Download failed, but pattern saved for future use")
                return None

        except Exception as e:
            self.add_comment(f"❌ Pattern capture error: {str(e)[:50]}")
            return None

    def test_learning_system(self):
        """Test the interactive learning system with a demo"""
        try:
            self.add_comment("🎓 Testing Interactive Learning System...")

            # Simulate finding a part but failing to download
            test_part = "TEST123"
            test_site = "UltraLibrarian"
            test_url = "https://www.ultralibrarian.com/search?q=TEST123"

            self.add_comment(f"🎯 Simulating: Found {test_part} on {test_site} but download failed")

            # This will trigger the learning dialog
            result = self.learn_download_pattern(test_site, test_part, test_url)

            if result:
                self.add_comment("✅ Learning system test completed successfully!")
            else:
                self.add_comment("ℹ️ Learning system test cancelled or skipped")

        except Exception as e:
            self.add_comment(f"❌ Learning system test error: {str(e)}")

    def show_help(self):
        """Show comprehensive help system"""
        try:
            help_window = tk.Toplevel(self.root)
            help_window.title("Component Finder - Help & Learning Guide")
            help_window.geometry("900x700")
            help_window.resizable(True, True)

            # Create notebook for different help sections
            notebook = ttk.Notebook(help_window)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Quick Start tab
            self.create_quick_start_tab(notebook)

            # Learning System tab
            self.create_learning_system_tab(notebook)

            # Excel Setup tab
            self.create_excel_setup_tab(notebook)

            # Troubleshooting tab
            self.create_troubleshooting_tab(notebook)

            # About tab
            self.create_about_tab(notebook)

        except Exception as e:
            messagebox.showerror("Help Error", f"Could not open help: {str(e)}")

    def create_quick_start_tab(self, notebook):
        """Create Quick Start help tab - loads from external file"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🚀 Quick Start")

        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Load help content from external file
        help_text = self.load_help_file("quick_start.txt")
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

    def create_learning_system_tab(self, notebook):
        """Create Learning System help tab - loads from external file"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🎓 Learning System")

        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Load help content from external file
        help_text = self.load_help_file("learning_system.txt")


        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

    def create_excel_setup_tab(self, notebook):
        """Create Excel Setup help tab - loads from external file"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📊 Excel Setup")

        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Load help content from external file
        help_text = self.load_help_file("excel_setup.txt")

        help_text = """
📊 EXCEL FILE SETUP GUIDE

═══════════════════════════════════════════════════════════════

📋 REQUIRED COLUMN STRUCTURE:

Your Excel file must have these columns:

Column F (6th column) = Manufacturer Name
   Examples: "TDK", "Panasonic", "Murata", "WURTH", "Texas Instruments"

Column G (7th column) = Part Number
   Examples: "C1005X5R1E105K050BC", "ERJ-1GNF1000C", "************"

Column H (8th column) = Datasheet Location
   Examples: "DATA-SHEETS\\TDK C1005.pdf", "nan" (if missing)

Column J (10th column) = STEP File Manufacturer
   Examples: "TDK", "TDK (UltraLibrarian)", "nan" (if missing)

Column K (11th column) = STEP File Location
   Examples: "C1005.step", "C1005_UltraLibrarian.step", "nan" (if missing)

═══════════════════════════════════════════════════════════════

📝 EXAMPLE EXCEL STRUCTURE:

| A | B | C | D | E | F           | G                    | H                        | I | J                | K                    |
|---|---|---|---|---|-------------|----------------------|--------------------------|---|------------------|----------------------|
|   |   |   |   |   | TDK         | C1005X5R1E105K050BC | DATA-SHEETS\\TDK C1005.pdf |   | nan              | nan                  |
|   |   |   |   |   | Panasonic   | ERJ-1GNF1000C       | DATA-SHEETS\\panasonic.pdf |   | Panasonic        | ERJ-1GNF1000C.step  |
|   |   |   |   |   | WURTH       | ************         | DATA-SHEETS\\WURTH.pdf     |   | WURTH            | STP_4351x10148xx.stp|

═══════════════════════════════════════════════════════════════

🔄 WHAT THE SYSTEM DOES:

ALWAYS (for every part):
   ✅ Downloads latest datasheet from Digi-Key/Mouser
   ✅ Keeps existing datasheet reference in Excel (doesn't overwrite)

FOR MISSING STEP FILES (Column K = "nan" or blank):
   ✅ Searches manufacturer website first
   ✅ If not found, searches UltraLibrarian, SnapMagic, SamacSys
   ✅ Updates Column K with filename
   ✅ Updates Column J with manufacturer + source

FOR MISSING DATASHEETS (Column H = "nan" or blank):
   ✅ Searches Digi-Key, Mouser
   ✅ If not found, enters learning mode
   ✅ Updates Column H with "DATA-SHEETS\\filename.pdf"

═══════════════════════════════════════════════════════════════

💾 FILE FORMATS SUPPORTED:
   ✅ .xlsx (Excel 2007+) - Recommended
   ✅ .xls (Excel 97-2003)

📦 REQUIRED PACKAGES:
   • pandas: pip install pandas
   • openpyxl: pip install openpyxl
   • xlrd: pip install xlrd

═══════════════════════════════════════════════════════════════

🎯 BEST PRACTICES:

1. BACKUP your Excel file before running searches
2. USE consistent manufacturer names (e.g., "TDK" not "tdk" or "T.D.K.")
3. PART NUMBERS should be exact (case-sensitive)
4. EMPTY CELLS should contain "nan" or be truly blank
5. PATHS use backslashes: "DATA-SHEETS\\filename.pdf"

═══════════════════════════════════════════════════════════════
"""
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

    def create_troubleshooting_tab(self, notebook):
        """Create Troubleshooting help tab - loads from external file"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🔧 Troubleshooting")

        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Load help content from external file
        help_text = self.load_help_file("troubleshooting.txt")

        help_text = """
🔧 TROUBLESHOOTING GUIDE

═══════════════════════════════════════════════════════════════

❌ COMMON ISSUES & SOLUTIONS:

📊 EXCEL FILE ISSUES:
   Problem: "Missing package: pandas/openpyxl"
   Solution: pip install pandas openpyxl xlrd

   Problem: Excel file won't load
   Solution: Check file format (.xlsx or .xls), ensure file isn't open in Excel

   Problem: Parts not found in Excel
   Solution: Check Column G has part numbers, ensure no extra spaces

🔍 SEARCH ISSUES:
   Problem: No datasheets found
   Solution: Check manufacturer name spelling, try learning mode

   Problem: No STEP files found
   Solution: Enable alternative sources search, use learning mode

   Problem: Downloads fail
   Solution: Check internet connection, try manual learning

🎓 LEARNING SYSTEM ISSUES:
   Problem: Learning dialogs don't appear
   Solution: Ensure learning_mode = True, check if part actually found on website

   Problem: Learned patterns don't work
   Solution: Check download_patterns.json file, patterns may need updating

   Problem: Browser doesn't open
   Solution: Check default browser settings, try different browser

═══════════════════════════════════════════════════════════════

🚀 ENHANCED SEARCH (SELENIUM):
   For better results on JavaScript-heavy sites:

   Install Selenium:
   pip install selenium

   Install ChromeDriver:
   1. Download from: https://chromedriver.chromium.org/
   2. Add to PATH or place in Python Scripts folder

   Alternative:
   pip install webdriver-manager

═══════════════════════════════════════════════════════════════

📁 FILE LOCATIONS:
   • Datasheets: DATA-SHEETS\\ folder
   • STEP files: Current directory or 3D-MODELS\\ folder
   • Patterns: download_patterns.json
   • Websites: actual-web-site-xref.csv
   • Knowledge: manufacturer_knowledge.json
   • Log: found-files-log.csv

═══════════════════════════════════════════════════════════════

🔄 RESET OPTIONS:
   • Delete download_patterns.json to reset learned patterns
   • Delete manufacturer_knowledge.json to reset website knowledge
   • Click "🧹 Cleanup Files" to organize downloaded files

═══════════════════════════════════════════════════════════════

📞 GETTING HELP:
   • Click "🎓 Test Learning" to test learning system
   • Check console output for detailed error messages
   • Learning mode shows step-by-step what's happening
   • Patterns are saved in human-readable JSON format

═══════════════════════════════════════════════════════════════
"""
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

    def create_about_tab(self, notebook):
        """Create About help tab - loads from external file"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="ℹ️ About")

        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Load help content from external file
        help_text = self.load_help_file("about.txt")

        help_text = """
ℹ️ COMPONENT FINDER - ABOUT

═══════════════════════════════════════════════════════════════

🎯 PURPOSE:
   Automatically find and download datasheets and 3D STEP files
   for electronic components from multiple sources.

🚀 KEY FEATURES:
   ✅ Excel integration for batch processing
   ✅ Interactive learning system
   ✅ Multiple source searching (Digi-Key, Mouser, manufacturer sites)
   ✅ Alternative 3D model sources (UltraLibrarian, SnapMagic, SamacSys)
   ✅ Automatic file organization
   ✅ Pattern learning and reuse
   ✅ Progress tracking and logging

═══════════════════════════════════════════════════════════════

🔍 SEARCH SOURCES:

DATASHEETS:
   • Digi-Key (primary)
   • Mouser (primary)
   • Manufacturer websites
   • Learning mode for custom sources

STEP FILES:
   • WURTH: we-online.com
   • UltraLibrarian: ultralibrarian.com
   • SnapMagic/SnapEDA: snapmagic.com, snapeda.com
   • SamacSys: componentsearchengine.com
   • Manufacturer websites
   • Learning mode for custom sources

═══════════════════════════════════════════════════════════════

🧠 LEARNING SYSTEM:
   The system learns from your manual actions:
   • Saves download patterns to download_patterns.json
   • Reuses patterns for similar parts
   • Tracks success rates
   • Continuously improves accuracy

═══════════════════════════════════════════════════════════════

📦 DEPENDENCIES:
   Core:
   • tkinter (GUI)
   • requests (HTTP)
   • beautifulsoup4 (HTML parsing)

   Excel Support:
   • pandas (data processing)
   • openpyxl (Excel files)
   • xlrd (legacy Excel files)

   Enhanced Search (Optional):
   • selenium (JavaScript support)
   • ChromeDriver (browser automation)

═══════════════════════════════════════════════════════════════

📊 VERSION HISTORY:
   • v1.0: Basic component search
   • v2.0: Excel integration
   • v3.0: Interactive learning system
   • v3.1: Enhanced alternative sources
   • v3.2: Comprehensive help system

═══════════════════════════════════════════════════════════════

🎓 LEARNING PHILOSOPHY:
   "The system gets smarter with every manual intervention"

   Instead of failing silently, the system asks for help,
   learns from your actions, and becomes more capable over time.

═══════════════════════════════════════════════════════════════
"""
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

    def extract_url_pattern(self, download_url, search_url):
        """Extract a reusable pattern from the download URL"""
        try:
            download_parsed = urlparse(download_url)
            search_parsed = urlparse(search_url)

            # Extract useful patterns
            patterns = []

            # Path pattern
            if download_parsed.path != search_parsed.path:
                patterns.append(f"path_contains:{download_parsed.path.split('/')[-1]}")

            # Query parameters
            if download_parsed.query:
                query_params = parse_qs(download_parsed.query)
                for key, values in query_params.items():
                    if key.lower() in ['download', 'file', 'format', 'type']:
                        patterns.append(f"param_{key}:{values[0]}")

            # File extension
            if download_url.lower().endswith(('.step', '.stp')):
                patterns.append("file_ext:step")

            return " | ".join(patterns) if patterns else "direct_link"

        except Exception as e:
            return "manual_pattern"

    def apply_learned_patterns(self, site_name, part_number, search_url):
        """Apply previously learned patterns to find download links"""
        try:
            site_key = site_name.lower().replace(' ', '_')
            if site_key not in self.download_patterns:
                return None

            patterns = self.download_patterns[site_key]
            self.add_comment(f"🧠 Applying {len(patterns)} learned patterns for {site_name}")

            # Try each learned pattern
            for pattern in patterns:
                try:
                    # Try to construct download URL based on pattern
                    if 'url_pattern' in pattern:
                        download_url = self.construct_download_url(search_url, pattern, part_number)
                        if download_url:
                            self.add_comment(f"🎯 Trying learned pattern: {pattern['description']}")

                            # Test if the URL works
                            success = self.download_file_from_url(download_url, "Unknown", part_number, "3d_model")
                            if success:
                                # Update success count
                                pattern['success_count'] = pattern.get('success_count', 0) + 1
                                self.save_download_patterns()

                                filename = f"{part_number}_{site_name}.step"
                                self.add_comment(f"✅ Success with learned pattern!")
                                return (download_url, filename, site_name)

                except Exception as e:
                    continue

            self.add_comment(f"❌ No learned patterns worked for {part_number}")
            return None

        except Exception as e:
            self.add_comment(f"❌ Pattern application error: {str(e)[:50]}")
            return None

    def construct_download_url(self, search_url, pattern, part_number):
        """Construct download URL from learned pattern"""
        try:
            # This is a simplified pattern matching
            # In practice, you'd implement more sophisticated pattern matching

            base_url = '/'.join(search_url.split('/')[:3])  # Get base URL

            # Try to substitute part number in the pattern
            if 'direct_link' in pattern.get('url_pattern', ''):
                return pattern.get('download_url', '').replace(pattern.get('part_number', ''), part_number)

            # Add more pattern matching logic here as needed
            return None

        except Exception as e:
            return None

def main():
    print("🚀 Starting Component Finder GUI...")
    print(f"📁 Working directory: {os.getcwd()}")

    try:
        print("🖥️ Creating tkinter root window...")
        root = tk.Tk()
        print("✅ Root window created successfully")

        # Force window to front
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(root.attributes, '-topmost', False)

        print("🔧 Initializing ComponentFinderGUI...")
        app = ComponentFinderGUI(root)
        print("✅ GUI initialized successfully")

        print("🎯 Starting main event loop...")
        print("📋 GUI should be visible now!")
        print("🔍 Look for window: 'Component Finder - Interactive Learning System'")

        root.mainloop()
        print("🏁 GUI closed")

    except Exception as e:
        print(f"❌ Error starting GUI: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    # Check if tkinter is available
    try:
        import tkinter as tk
        from tkinter import ttk, scrolledtext, messagebox, simpledialog
        main()
    except ImportError:
        print("❌ Tkinter not available. Please install tkinter for GUI support.")
        print("   Alternative: Use the command-line version: learning_component_finder.py")
